# 直播自动化处理系统

一个从直播录制到自动剪辑上传的完整自动化工作流系统。

## 🎯 项目概述

### 核心功能
- 🎬 **自动录制**：监听直播并自动录制
- 🤖 **智能分析**：AI分析视频内容，提取精华片段
- ✏️ **标题生成**：自动生成吸引人的视频标题
- 🎨 **自动剪辑**：模板化剪辑，添加字幕和水印
- 📱 **用户确认**：手机端快速确认和调整
- 🚀 **自动上传**：批量上传到快手等平台

### 技术架构
- **后端**：FastAPI + SQLite + WebSocket
- **前端**：Vue 3 + Element Plus + Vite
- **AI服务**：DeepSeek API + Whisper
- **视频处理**：FFmpeg + OpenCV + pyJianYingDraft

## 🏗️ 系统架构

```
录制库 → 调度层 → 视频分析服务 → 用户确认 → 上传服务
   ↓        ↓           ↓            ↓         ↓
 录制视频   任务管理    AI分析       手机确认   自动上传
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Node.js 16+
- FFmpeg
- Chrome/Chromium

### 一键启动
```bash
# 1. 克隆项目
git clone https://gitee.com/pulse-back/video-analysis.git
cd video-analysis

# 2. 安装依赖
python start.py install

# 3. 启动开发环境
python start.py dev
```

### 手动启动
```bash
# 调度层
cd scheduler && pip install -r requirements.txt && python app.py

# 视频分析服务
cd video_analysis && pip install -r requirements.txt && python app.py

# 上传服务
cd auto_upload && pip install -r requirements.txt && python api_server.py

# 前端界面
cd frontend && npm install && npm run dev
```

### 访问地址
- 前端界面：http://localhost:8888
- 调度层API：http://localhost:8080
- 视频分析API：http://localhost:8000
- 上传服务API：http://localhost:8001

## 📁 项目结构

```
VideoAnalysis/
├── scheduler/                   # 调度层服务
├── video_analysis/             # 视频分析服务
├── auto_upload/                # 上传服务
├── frontend/                   # 前端界面
├── DouyinLiveRecorder-main/    # 录制库
├── docs/                       # 项目文档
├── start.py                    # 统一启动脚本
├── docker-compose.yml          # Docker部署
└── README.md                   # 项目说明
```

## 📖 开发文档

- [架构设计文档](./直播自动化处理系统-架构设计文档.md)
- [总体任务列表](./总体任务列表.md)
- [调度层开发文档](./scheduler/README.md)
- [视频分析开发文档](./video_analysis/README.md)
- [前端开发文档](./frontend/README.md)
- [启动脚本说明](./启动脚本.md)

## 🔧 开发进度

- [x] 项目架构设计
- [x] 基础框架搭建
- [ ] 调度层开发
- [ ] 视频分析服务开发
- [ ] 前端界面开发
- [ ] 系统集成测试

详细进度请查看：[总体任务列表](./总体任务列表.md)

## 🤝 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系方式

- 项目地址：https://gitee.com/pulse-back/video-analysis
- 问题反馈：https://gitee.com/pulse-back/video-analysis/issues

## 🙏 致谢

感谢以下开源项目：
- [FastAPI](https://fastapi.tiangolo.com/)
- [Vue.js](https://vuejs.org/)
- [Element Plus](https://element-plus.org/)
- [OpenAI Whisper](https://github.com/openai/whisper)
- [pyJianYingDraft](https://github.com/GuanYixuan/pyJianYingDraft)
