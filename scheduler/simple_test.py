"""
简单的调度层功能测试
"""
import sys
import asyncio
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


async def test_basic_functionality():
    """测试基础功能"""
    print("🔍 测试基础功能...")
    
    try:
        # 测试配置加载
        print("1. 测试配置加载...")
        from config.settings import settings
        print(f"   ✅ 配置加载成功: {settings.app_name}")
        
        # 测试数据库配置
        print("2. 测试数据库配置...")
        from config.database import Base, engine
        print(f"   ✅ 数据库配置成功: {settings.database_url}")
        
        # 测试数据模型
        print("3. 测试数据模型...")
        from models.task import Task, TaskStatus
        print(f"   ✅ 任务模型加载成功")
        
        # 测试工具模块
        print("4. 测试工具模块...")
        from utils.logger import get_logger
        from utils.helpers import generate_task_id, validate_video_path
        logger = get_logger(__name__)
        task_id = generate_task_id()
        print(f"   ✅ 工具模块加载成功, 生成任务ID: {task_id[:8]}...")
        
        # 测试任务管理器
        print("5. 测试任务管理器...")
        from services.task_manager import TaskManager
        task_manager = TaskManager()
        print(f"   ✅ 任务管理器创建成功")
        
        # 测试数据库初始化
        print("6. 测试数据库初始化...")
        from database.init_db import create_tables
        success = await create_tables()
        if success:
            print(f"   ✅ 数据库表创建成功")
        else:
            print(f"   ⚠️  数据库表创建失败")
        
        # 测试创建任务
        print("7. 测试创建任务...")
        import tempfile
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as temp_file:
            temp_file.write(b"fake video content")
            video_path = temp_file.name
        
        try:
            task = await task_manager.create_task(
                video_path=video_path,
                streamer="测试主播",
                platform="douyin",
                metadata={"test": True}
            )
            print(f"   ✅ 任务创建成功: {task.id}")
            
            # 测试获取任务
            retrieved_task = await task_manager.get_task(task.id)
            if retrieved_task:
                print(f"   ✅ 任务获取成功: {retrieved_task.streamer}")
            else:
                print(f"   ❌ 任务获取失败")
            
            # 测试更新任务状态
            success = await task_manager.update_task_status(
                task.id, 
                TaskStatus.ANALYZING, 
                progress=50,
                current_step="video_analysis"
            )
            if success:
                print(f"   ✅ 任务状态更新成功")
            else:
                print(f"   ❌ 任务状态更新失败")
                
        finally:
            # 清理临时文件
            try:
                os.unlink(video_path)
            except:
                pass
        
        print("\n🎉 所有基础功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("调度层基础功能测试")
    print("=" * 50)
    
    success = await test_basic_functionality()
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n测试结果: {'成功' if success else '失败'}")
    exit(0 if success else 1)
