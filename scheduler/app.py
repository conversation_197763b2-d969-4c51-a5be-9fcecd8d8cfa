"""
调度层 FastAPI 应用主文件
"""
import asyncio
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config.settings import settings
from config.database import init_database, close_database
from api.routes import tasks_router, system_router, websocket_router
from services.notification_service import notification_service
from utils.logger import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("调度层服务启动中...")

    # 初始化数据库
    await init_database()

    # 启动通知服务
    await notification_service.start()

    logger.info("调度层服务启动完成")

    yield

    # 关闭时执行
    logger.info("调度层服务关闭中...")

    # 停止通知服务
    await notification_service.stop()

    # 关闭数据库连接
    await close_database()

    logger.info("调度层服务关闭完成")


# 创建FastAPI应用
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="直播自动化处理系统 - 调度层服务",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(tasks_router)
app.include_router(system_router)
app.include_router(websocket_router)


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "调度层服务运行中",
        "version": settings.app_version,
        "status": "healthy"
    }


@app.get("/health")
async def health_check():
    """简单健康检查"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
