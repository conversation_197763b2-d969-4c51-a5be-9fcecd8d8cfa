"""
数据库初始化脚本
"""
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine
from config.database import Base, engine
from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


async def create_tables():
    """创建数据库表"""
    try:
        async with engine.begin() as conn:
            # 创建所有表
            await conn.run_sync(Base.metadata.create_all)
        logger.info("数据库表创建成功")
        return True
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        return False


async def drop_tables():
    """删除数据库表"""
    try:
        async with engine.begin() as conn:
            # 删除所有表
            await conn.run_sync(Base.metadata.drop_all)
        logger.info("数据库表删除成功")
        return True
    except Exception as e:
        logger.error(f"删除数据库表失败: {e}")
        return False


async def init_database():
    """初始化数据库"""
    logger.info("开始初始化数据库...")
    
    # 创建表
    success = await create_tables()
    if not success:
        return False
    
    logger.info("数据库初始化完成")
    return True


async def reset_database():
    """重置数据库"""
    logger.info("开始重置数据库...")
    
    # 删除表
    await drop_tables()
    
    # 重新创建表
    success = await create_tables()
    
    if success:
        logger.info("数据库重置完成")
    else:
        logger.error("数据库重置失败")
    
    return success


if __name__ == "__main__":
    # 直接运行此脚本来初始化数据库
    asyncio.run(init_database())
