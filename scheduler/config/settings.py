"""
调度层配置管理
"""
import os
from typing import Optional
try:
    from pydantic_settings import BaseSettings
except ImportError:
    from pydantic import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 应用基础配置
    app_name: str = "调度层服务"
    app_version: str = "1.0.0"
    debug: bool = True
    
    # 服务器配置
    host: str = "0.0.0.0"
    port: int = 8081
    
    # 数据库配置
    database_url: str = "sqlite+aiosqlite:///./scheduler.db"
    database_echo: bool = False
    
    # 外部服务配置
    video_analysis_url: str = "http://localhost:8000"
    upload_service_url: str = "http://localhost:8001"
    
    # 任务配置
    max_concurrent_tasks: int = 10
    task_timeout: int = 3600  # 1小时
    retry_max_attempts: int = 3
    retry_delay: int = 5  # 秒
    
    # WebSocket配置
    websocket_heartbeat_interval: int = 30
    websocket_max_connections: int = 100
    
    # 日志配置
    log_level: str = "INFO"
    log_file: str = "./logs/scheduler.log"
    
    # 安全配置
    secret_key: str = "your-secret-key-here"
    access_token_expire_minutes: int = 30
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# 全局配置实例
settings = Settings()
