"""
辅助函数模块
"""
import os
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from pathlib import Path


def generate_task_id() -> str:
    """生成任务ID"""
    return str(uuid.uuid4())


def format_duration(seconds: float) -> str:
    """格式化时长"""
    if seconds < 60:
        return f"{seconds:.1f}秒"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}分钟"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}小时"


def validate_video_path(video_path: str) -> bool:
    """验证视频文件路径"""
    if not video_path:
        return False
    
    path = Path(video_path)
    
    # 检查文件是否存在
    if not path.exists():
        return False
    
    # 检查是否为文件
    if not path.is_file():
        return False
    
    # 检查文件扩展名
    valid_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.flv', '.ts', '.m4v'}
    if path.suffix.lower() not in valid_extensions:
        return False
    
    return True


def get_file_size(file_path: str) -> Optional[int]:
    """获取文件大小"""
    try:
        return os.path.getsize(file_path)
    except (OSError, FileNotFoundError):
        return None


def get_video_duration(video_path: str) -> Optional[float]:
    """获取视频时长（需要ffprobe）"""
    # TODO: 实现视频时长获取
    # 这里可以使用ffprobe或其他工具
    return None


def sanitize_filename(filename: str) -> str:
    """清理文件名，移除非法字符"""
    invalid_chars = '<>:"/\\|?*'
    for char in invalid_chars:
        filename = filename.replace(char, '_')
    return filename.strip()


def calculate_progress(current_step: str, total_steps: list) -> int:
    """计算任务进度百分比"""
    if current_step not in total_steps:
        return 0
    
    current_index = total_steps.index(current_step)
    return int((current_index / len(total_steps)) * 100)


def is_recent_file(file_path: str, hours: int = 24) -> bool:
    """检查文件是否为最近创建"""
    try:
        file_time = datetime.fromtimestamp(os.path.getctime(file_path))
        return datetime.now() - file_time < timedelta(hours=hours)
    except (OSError, FileNotFoundError):
        return False


def merge_metadata(base_metadata: Dict[str, Any], new_metadata: Dict[str, Any]) -> Dict[str, Any]:
    """合并元数据"""
    result = base_metadata.copy()
    result.update(new_metadata)
    return result


def extract_streamer_from_path(video_path: str) -> Optional[str]:
    """从视频路径中提取主播名称"""
    path = Path(video_path)
    
    # 尝试从父目录名称提取
    parent_name = path.parent.name
    if parent_name and parent_name != "recordings":
        return parent_name
    
    # 尝试从文件名提取
    filename = path.stem
    parts = filename.split('_')
    if len(parts) > 1:
        return parts[0]
    
    return None
