"""
调度层API功能测试脚本
"""
import asyncio
import aiohttp
import json
import tempfile
import os
from datetime import datetime


class APITester:
    """API测试器"""
    
    def __init__(self, base_url="http://localhost:8081"):
        self.base_url = base_url
        self.session = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    async def test_health_check(self):
        """测试健康检查"""
        print("🔍 测试健康检查...")
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 健康检查通过: {data}")
                    return True
                else:
                    print(f"❌ 健康检查失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    async def test_root_endpoint(self):
        """测试根路径"""
        print("🔍 测试根路径...")
        try:
            async with self.session.get(f"{self.base_url}/") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 根路径访问成功: {data.get('message')}")
                    return True
                else:
                    print(f"❌ 根路径访问失败: {response.status}")
                    return False
        except Exception as e:
            print(f"❌ 根路径访问异常: {e}")
            return False
    
    async def test_system_status(self):
        """测试系统状态"""
        print("🔍 测试系统状态...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/system/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 系统状态获取成功:")
                    print(f"   - 状态: {data.get('status')}")
                    print(f"   - 版本: {data.get('version')}")
                    print(f"   - 运行时间: {data.get('uptime'):.2f}秒")
                    print(f"   - 活跃任务: {data.get('active_tasks')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 系统状态获取失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 系统状态获取异常: {e}")
            return False
    
    async def test_system_config(self):
        """测试系统配置"""
        print("🔍 测试系统配置...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/system/config") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 系统配置获取成功:")
                    print(f"   - 最大并发任务: {data.get('max_concurrent_tasks')}")
                    print(f"   - 任务超时: {data.get('task_timeout')}秒")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 系统配置获取失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 系统配置获取异常: {e}")
            return False
    
    async def test_health_check_detailed(self):
        """测试详细健康检查"""
        print("🔍 测试详细健康检查...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/system/health") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 详细健康检查成功:")
                    print(f"   - 整体状态: {data.get('status')}")
                    print(f"   - 数据库状态: {data.get('database')}")
                    print(f"   - 服务状态: {data.get('services')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 详细健康检查失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 详细健康检查异常: {e}")
            return False
    
    async def test_create_task(self):
        """测试创建任务"""
        print("🔍 测试创建任务...")
        
        # 创建临时视频文件
        with tempfile.NamedTemporaryFile(suffix=".mp4", delete=False) as temp_file:
            temp_file.write(b"fake video content for testing")
            video_path = temp_file.name
        
        try:
            payload = {
                "video_path": video_path,
                "duration": 600.5,
                "streamer": "测试主播API",
                "platform": "douyin",
                "timestamp": datetime.utcnow().isoformat(),
                "file_size": 1024000,
                "metadata": {
                    "test": True,
                    "quality": "原画",
                    "api_test": True
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/api/v1/recording/completed",
                json=payload
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    task_id = data.get("id")
                    print(f"✅ 任务创建成功:")
                    print(f"   - 任务ID: {task_id}")
                    print(f"   - 状态: {data.get('status')}")
                    print(f"   - 主播: {data.get('streamer')}")
                    return task_id
                else:
                    error_text = await response.text()
                    print(f"❌ 任务创建失败: {response.status}, {error_text}")
                    return None
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
            return None
        finally:
            # 清理临时文件
            try:
                os.unlink(video_path)
            except:
                pass
    
    async def test_get_task_status(self, task_id):
        """测试获取任务状态"""
        if not task_id:
            return False
            
        print(f"🔍 测试获取任务状态: {task_id[:8]}...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/tasks/{task_id}/status") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 任务状态获取成功:")
                    print(f"   - 状态: {data.get('status')}")
                    print(f"   - 进度: {data.get('progress')}%")
                    print(f"   - 当前步骤: {data.get('current_step')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 任务状态获取失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 任务状态获取异常: {e}")
            return False
    
    async def test_get_tasks_list(self):
        """测试获取任务列表"""
        print("🔍 测试获取任务列表...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/tasks?page=1&size=10") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 任务列表获取成功:")
                    print(f"   - 总数: {data.get('total')}")
                    print(f"   - 当前页: {data.get('page')}")
                    print(f"   - 任务数: {len(data.get('tasks', []))}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 任务列表获取失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 任务列表获取异常: {e}")
            return False
    
    async def test_get_active_tasks(self):
        """测试获取活跃任务"""
        print("🔍 测试获取活跃任务...")
        try:
            async with self.session.get(f"{self.base_url}/api/v1/tasks/active") as response:
                if response.status == 200:
                    data = await response.json()
                    print(f"✅ 活跃任务获取成功:")
                    print(f"   - 活跃任务数: {data.get('count')}")
                    return True
                else:
                    error_text = await response.text()
                    print(f"❌ 活跃任务获取失败: {response.status}, {error_text}")
                    return False
        except Exception as e:
            print(f"❌ 活跃任务获取异常: {e}")
            return False
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始调度层API功能测试...\n")
        
        results = []
        
        # 基础测试
        results.append(await self.test_health_check())
        print()
        
        results.append(await self.test_root_endpoint())
        print()
        
        # 系统API测试
        results.append(await self.test_system_status())
        print()
        
        results.append(await self.test_system_config())
        print()
        
        results.append(await self.test_health_check_detailed())
        print()
        
        # 任务API测试
        task_id = await self.test_create_task()
        results.append(task_id is not None)
        print()
        
        if task_id:
            results.append(await self.test_get_task_status(task_id))
            print()
        else:
            results.append(False)
        
        results.append(await self.test_get_tasks_list())
        print()
        
        results.append(await self.test_get_active_tasks())
        print()
        
        # 测试结果统计
        passed = sum(results)
        total = len(results)
        
        print("📊 API测试结果统计:")
        print(f"   - 总测试数: {total}")
        print(f"   - 通过数: {passed}")
        print(f"   - 失败数: {total - passed}")
        print(f"   - 成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("🎉 所有API测试通过！")
        else:
            print("⚠️  部分API测试失败，请检查日志")
        
        return passed == total


async def main():
    """主函数"""
    print("调度层API功能测试")
    print("=" * 50)
    
    async with APITester() as tester:
        success = await tester.run_all_tests()
        return success


if __name__ == "__main__":
    success = asyncio.run(main())
    print(f"\n测试结果: {'成功' if success else '失败'}")
    exit(0 if success else 1)
