"""
系统相关API数据模式
"""
from datetime import datetime
from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field


class SystemStatus(BaseModel):
    """系统状态"""
    status: str = Field(..., description="系统状态")
    version: str = Field(..., description="系统版本")
    uptime: float = Field(..., description="运行时间(秒)")
    active_tasks: int = Field(..., description="活跃任务数")
    total_tasks: int = Field(..., description="总任务数")
    websocket_connections: int = Field(..., description="WebSocket连接数")
    memory_usage: Optional[float] = Field(None, description="内存使用率")
    cpu_usage: Optional[float] = Field(None, description="CPU使用率")
    disk_usage: Optional[float] = Field(None, description="磁盘使用率")
    last_updated: datetime = Field(..., description="最后更新时间")


class SystemConfig(BaseModel):
    """系统配置"""
    max_concurrent_tasks: int = Field(..., description="最大并发任务数")
    task_timeout: int = Field(..., description="任务超时时间(秒)")
    retry_max_attempts: int = Field(..., description="最大重试次数")
    retry_delay: int = Field(..., description="重试延迟(秒)")
    video_analysis_url: str = Field(..., description="视频分析服务地址")
    upload_service_url: str = Field(..., description="上传服务地址")
    websocket_heartbeat_interval: int = Field(..., description="WebSocket心跳间隔(秒)")
    log_level: str = Field(..., description="日志级别")


class HealthCheck(BaseModel):
    """健康检查"""
    status: str = Field(..., description="健康状态")
    timestamp: datetime = Field(..., description="检查时间")
    services: Dict[str, str] = Field(..., description="服务状态")
    database: str = Field(..., description="数据库状态")
    dependencies: Dict[str, Any] = Field(..., description="依赖服务状态")


class ServiceInfo(BaseModel):
    """服务信息"""
    name: str = Field(..., description="服务名称")
    url: str = Field(..., description="服务地址")
    status: str = Field(..., description="服务状态")
    last_check: datetime = Field(..., description="最后检查时间")
    response_time: Optional[float] = Field(None, description="响应时间(毫秒)")


class TaskStatistics(BaseModel):
    """任务统计"""
    total_tasks: int = Field(..., description="总任务数")
    completed_tasks: int = Field(..., description="已完成任务数")
    failed_tasks: int = Field(..., description="失败任务数")
    active_tasks: int = Field(..., description="活跃任务数")
    average_processing_time: Optional[float] = Field(None, description="平均处理时间(秒)")
    success_rate: Optional[float] = Field(None, description="成功率")
    tasks_by_status: Dict[str, int] = Field(default_factory=dict, description="按状态分组的任务数")
    tasks_by_platform: Dict[str, int] = Field(default_factory=dict, description="按平台分组的任务数")
