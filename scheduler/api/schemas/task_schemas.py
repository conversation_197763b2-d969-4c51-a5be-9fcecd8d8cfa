"""
任务相关API数据模式
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field


class TaskBase(BaseModel):
    """任务基础模式"""
    video_path: str = Field(..., description="视频文件路径")
    streamer: Optional[str] = Field(None, description="主播名称")
    platform: Optional[str] = Field(None, description="平台名称")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")


class TaskCreate(TaskBase):
    """创建任务请求"""
    pass


class TaskUpdate(BaseModel):
    """更新任务请求"""
    status: Optional[str] = Field(None, description="任务状态")
    progress: Optional[int] = Field(None, ge=0, le=100, description="进度百分比")
    current_step: Optional[str] = Field(None, description="当前步骤")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class TaskResponse(BaseModel):
    """任务响应"""
    id: str = Field(..., description="任务ID")
    video_path: str = Field(..., description="视频文件路径")
    streamer: Optional[str] = Field(None, description="主播名称")
    platform: Optional[str] = Field(None, description="平台名称")
    status: str = Field(..., description="任务状态")
    progress: int = Field(..., description="进度百分比")
    current_step: Optional[str] = Field(None, description="当前步骤")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="元数据")

    class Config:
        from_attributes = True


class TaskListResponse(BaseModel):
    """任务列表响应"""
    tasks: List[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="页码")
    size: int = Field(..., description="每页大小")


class RecordingCompletedRequest(BaseModel):
    """录制完成通知请求"""
    video_path: str = Field(..., description="视频文件路径")
    duration: Optional[float] = Field(None, description="视频时长(秒)")
    streamer: Optional[str] = Field(None, description="主播名称")
    platform: Optional[str] = Field(None, description="平台名称")
    timestamp: Optional[datetime] = Field(None, description="录制时间")
    file_size: Optional[int] = Field(None, description="文件大小(字节)")
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外元数据")


class UserConfirmationRequest(BaseModel):
    """用户确认请求"""
    selected_highlight: Optional[str] = Field(None, description="选择的精华片段ID")
    selected_title: Optional[str] = Field(None, description="选择的标题")
    custom_title: Optional[str] = Field(None, description="自定义标题")
    upload_platforms: List[str] = Field(default_factory=list, description="上传平台列表")
    additional_settings: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外设置")
