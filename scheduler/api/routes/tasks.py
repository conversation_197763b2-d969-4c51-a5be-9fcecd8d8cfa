"""
任务相关API路由
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from config.database import get_db
from models.task import Task, TaskStatus
from api.schemas.task_schemas import (
    TaskResponse, TaskListResponse, RecordingCompletedRequest,
    UserConfirmationRequest, TaskCreate, TaskUpdate
)
from services.task_manager import task_manager
from services.workflow_orchestrator import workflow_orchestrator
from services.notification_service import notification_service
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1", tags=["任务管理"])


def task_to_response(task: Task) -> TaskResponse:
    """将Task模型转换为TaskResponse"""
    return TaskResponse(
        id=task.id,
        video_path=task.video_path,
        streamer=task.streamer,
        platform=task.platform,
        status=task.status,
        progress=task.progress,
        current_step=task.current_step,
        created_at=task.created_at,
        updated_at=task.updated_at,
        completed_at=task.completed_at,
        metadata=task.task_metadata or {}
    )


@router.post("/recording/completed", response_model=TaskResponse)
async def recording_completed(
    request: RecordingCompletedRequest,
    background_tasks: BackgroundTasks
):
    """录制完成通知"""
    try:
        # 创建新任务
        task = await task_manager.create_task(
            video_path=request.video_path,
            streamer=request.streamer,
            platform=request.platform,
            metadata={
                "duration": request.duration,
                "timestamp": request.timestamp.isoformat() if request.timestamp else None,
                "file_size": request.file_size,
                **request.metadata
            }
        )
        
        # 启动工作流
        background_tasks.add_task(workflow_orchestrator.start_workflow, task.id)
        
        # 发送通知
        await notification_service.send_task_notification(
            task.id, 
            "task.created", 
            {"message": f"新任务已创建: {request.streamer or '未知主播'}"}
        )
        
        logger.info(f"录制完成通知处理成功: {task.id}")
        return task_to_response(task)
        
    except Exception as e:
        logger.error(f"录制完成通知处理失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/{task_id}/status", response_model=TaskResponse)
async def get_task_status(task_id: str):
    """获取任务状态"""
    task = await task_manager.get_task(task_id)
    if not task:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task_to_response(task)


@router.get("/tasks/pending", response_model=TaskListResponse)
async def get_pending_tasks(
    page: int = 1,
    size: int = 20,
    platform: Optional[str] = None
):
    """获取待处理任务列表"""
    try:
        offset = (page - 1) * size
        tasks = await task_manager.get_tasks(
            status=TaskStatus.WAITING_CONFIRM,
            platform=platform,
            limit=size,
            offset=offset
        )
        
        # 获取总数（简化实现）
        total = len(tasks)
        
        return TaskListResponse(
            tasks=[task_to_response(task) for task in tasks],
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取待处理任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/tasks/{task_id}/confirm")
async def confirm_task(
    task_id: str,
    request: UserConfirmationRequest,
    background_tasks: BackgroundTasks
):
    """用户确认任务"""
    try:
        # 检查任务是否存在
        task = await task_manager.get_task(task_id)
        if not task:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        if task.status != TaskStatus.WAITING_CONFIRM:
            raise HTTPException(status_code=400, detail="任务状态不正确")
        
        # 处理用户确认
        success = await workflow_orchestrator.handle_user_confirmation(
            task_id, 
            request.dict()
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="处理用户确认失败")
        
        # 发送通知
        await notification_service.send_task_notification(
            task_id, 
            "task.confirmed", 
            {"message": "用户确认完成，开始生成视频"}
        )
        
        return {"message": "确认成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"用户确认失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/tasks/{task_id}")
async def delete_task(task_id: str):
    """删除任务"""
    try:
        # 停止工作流
        await workflow_orchestrator.stop_workflow(task_id)
        
        # 删除任务
        success = await task_manager.delete_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        # 发送通知
        await notification_service.send_task_notification(
            task_id, 
            "task.deleted", 
            {"message": "任务已删除"}
        )
        
        return {"message": "删除成功", "task_id": task_id}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {task_id}, 错误: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks", response_model=TaskListResponse)
async def get_tasks(
    page: int = 1,
    size: int = 20,
    status: Optional[str] = None,
    platform: Optional[str] = None
):
    """获取任务列表"""
    try:
        offset = (page - 1) * size
        tasks = await task_manager.get_tasks(
            status=status,
            platform=platform,
            limit=size,
            offset=offset
        )
        
        # 获取总数（简化实现）
        total = len(tasks)
        
        return TaskListResponse(
            tasks=[task_to_response(task) for task in tasks],
            total=total,
            page=page,
            size=size
        )
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/tasks/active")
async def get_active_tasks():
    """获取活跃任务列表"""
    try:
        tasks = await task_manager.get_active_tasks()
        return {
            "active_tasks": [task_to_response(task) for task in tasks],
            "count": len(tasks)
        }
    except Exception as e:
        logger.error(f"获取活跃任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
