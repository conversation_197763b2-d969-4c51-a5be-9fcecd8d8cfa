"""
系统相关API路由
"""
import time
import psutil
from datetime import datetime
from fastapi import APIRouter, HTTPException
from api.schemas.system_schemas import SystemStatus, SystemConfig, HealthCheck
from config.settings import settings
from services.external_api import external_api
from services.notification_service import notification_service
from services.task_manager import task_manager
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(prefix="/api/v1/system", tags=["系统管理"])

# 系统启动时间
_start_time = time.time()


@router.get("/status", response_model=SystemStatus)
async def get_system_status():
    """获取系统状态"""
    try:
        # 获取活跃任务
        active_tasks = await task_manager.get_active_tasks()
        
        # 获取所有任务数量（简化实现）
        all_tasks = await task_manager.get_tasks(limit=1000)
        
        # 获取WebSocket连接数
        ws_connections = notification_service.get_websocket_manager().get_connection_count()
        
        # 获取系统资源使用情况
        try:
            memory_usage = psutil.virtual_memory().percent
            cpu_usage = psutil.cpu_percent(interval=1)
            disk_usage = psutil.disk_usage('/').percent
        except:
            memory_usage = None
            cpu_usage = None
            disk_usage = None
        
        return SystemStatus(
            status="running",
            version=settings.app_version,
            uptime=time.time() - _start_time,
            active_tasks=len(active_tasks),
            total_tasks=len(all_tasks),
            websocket_connections=ws_connections,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            disk_usage=disk_usage,
            last_updated=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/config", response_model=SystemConfig)
async def get_system_config():
    """获取系统配置"""
    try:
        return SystemConfig(
            max_concurrent_tasks=settings.max_concurrent_tasks,
            task_timeout=settings.task_timeout,
            retry_max_attempts=settings.retry_max_attempts,
            retry_delay=settings.retry_delay,
            video_analysis_url=settings.video_analysis_url,
            upload_service_url=settings.upload_service_url,
            websocket_heartbeat_interval=settings.websocket_heartbeat_interval,
            log_level=settings.log_level
        )
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/config")
async def update_system_config(config: SystemConfig):
    """更新系统配置"""
    try:
        # 更新配置（这里简化实现，实际应该持久化到配置文件或数据库）
        settings.max_concurrent_tasks = config.max_concurrent_tasks
        settings.task_timeout = config.task_timeout
        settings.retry_max_attempts = config.retry_max_attempts
        settings.retry_delay = config.retry_delay
        settings.video_analysis_url = config.video_analysis_url
        settings.upload_service_url = config.upload_service_url
        settings.websocket_heartbeat_interval = config.websocket_heartbeat_interval
        settings.log_level = config.log_level
        
        # 发送配置更新通知
        await notification_service.send_system_notification(
            "system.config_updated",
            {"message": "系统配置已更新"}
        )
        
        logger.info("系统配置更新成功")
        return {"message": "配置更新成功"}
        
    except Exception as e:
        logger.error(f"更新系统配置失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health", response_model=HealthCheck)
async def health_check():
    """健康检查"""
    try:
        # 检查数据库连接
        try:
            # 简单的数据库连接测试
            await task_manager.get_tasks(limit=1)
            database_status = "healthy"
        except Exception as e:
            logger.error(f"数据库健康检查失败: {e}")
            database_status = "unhealthy"
        
        # 检查外部服务
        services_health = await external_api.get_all_services_health()
        
        # 检查核心服务
        services = {
            "task_manager": "healthy",
            "workflow_orchestrator": "healthy",
            "notification_service": "healthy"
        }
        
        # 整体健康状态
        overall_status = "healthy"
        if database_status != "healthy":
            overall_status = "degraded"
        
        if not all(services_health.values()):
            overall_status = "degraded"
        
        return HealthCheck(
            status=overall_status,
            timestamp=datetime.utcnow(),
            services=services,
            database=database_status,
            dependencies=services_health
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthCheck(
            status="unhealthy",
            timestamp=datetime.utcnow(),
            services={"error": str(e)},
            database="unknown",
            dependencies={}
        )


@router.post("/cleanup")
async def cleanup_old_tasks(days: int = 30):
    """清理旧任务"""
    try:
        if days < 1:
            raise HTTPException(status_code=400, detail="天数必须大于0")
        
        cleaned_count = await task_manager.cleanup_old_tasks(days)
        
        logger.info(f"清理了 {cleaned_count} 个旧任务")
        return {
            "message": f"清理完成，删除了 {cleaned_count} 个任务",
            "cleaned_count": cleaned_count
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清理旧任务失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/workflows")
async def get_running_workflows():
    """获取运行中的工作流"""
    try:
        from ...services.workflow_orchestrator import workflow_orchestrator
        workflows = workflow_orchestrator.get_running_workflows()
        
        return {
            "running_workflows": workflows,
            "count": len(workflows)
        }
        
    except Exception as e:
        logger.error(f"获取运行中工作流失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
