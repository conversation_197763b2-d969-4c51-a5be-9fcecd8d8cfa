"""
WebSocket相关API路由
"""
import json
import uuid
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from services.notification_service import notification_service
from utils.logger import get_logger

logger = get_logger(__name__)
router = APIRouter(tags=["WebSocket"])


@router.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket连接端点"""
    connection_id = str(uuid.uuid4())
    ws_manager = notification_service.get_websocket_manager()
    
    try:
        # 建立连接
        await ws_manager.connect(websocket, connection_id)
        logger.info(f"WebSocket连接建立: {connection_id}")
        
        # 发送欢迎消息
        await ws_manager.send_personal_message(
            {
                "type": "connection",
                "event": "connected",
                "data": {
                    "connection_id": connection_id,
                    "message": "WebSocket连接成功"
                }
            },
            connection_id
        )
        
        # 监听客户端消息
        while True:
            try:
                # 接收消息
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # 处理消息
                await handle_websocket_message(connection_id, message, ws_manager)
                
            except WebSocketDisconnect:
                logger.info(f"WebSocket客户端主动断开: {connection_id}")
                break
            except json.JSONDecodeError:
                # 发送错误消息
                await ws_manager.send_personal_message(
                    {
                        "type": "error",
                        "message": "无效的JSON格式"
                    },
                    connection_id
                )
            except Exception as e:
                logger.error(f"处理WebSocket消息异常: {connection_id}, 错误: {e}")
                await ws_manager.send_personal_message(
                    {
                        "type": "error",
                        "message": f"处理消息失败: {str(e)}"
                    },
                    connection_id
                )
                
    except Exception as e:
        logger.error(f"WebSocket连接异常: {connection_id}, 错误: {e}")
    finally:
        # 断开连接
        ws_manager.disconnect(connection_id)
        logger.info(f"WebSocket连接清理完成: {connection_id}")


async def handle_websocket_message(
    connection_id: str, 
    message: dict, 
    ws_manager
):
    """处理WebSocket消息"""
    message_type = message.get("type")
    
    if message_type == "ping":
        # 心跳检测
        await ws_manager.send_personal_message(
            {
                "type": "pong",
                "timestamp": message.get("timestamp")
            },
            connection_id
        )
        
    elif message_type == "subscribe":
        # 订阅任务通知
        task_id = message.get("task_id")
        if task_id:
            ws_manager.subscribe_task(connection_id, task_id)
            await ws_manager.send_personal_message(
                {
                    "type": "subscription",
                    "event": "subscribed",
                    "data": {
                        "task_id": task_id,
                        "message": f"已订阅任务 {task_id} 的通知"
                    }
                },
                connection_id
            )
            logger.info(f"连接 {connection_id} 订阅任务 {task_id}")
        else:
            await ws_manager.send_personal_message(
                {
                    "type": "error",
                    "message": "缺少task_id参数"
                },
                connection_id
            )
            
    elif message_type == "unsubscribe":
        # 取消订阅任务通知
        task_id = message.get("task_id")
        if task_id:
            ws_manager.unsubscribe_task(connection_id, task_id)
            await ws_manager.send_personal_message(
                {
                    "type": "subscription",
                    "event": "unsubscribed",
                    "data": {
                        "task_id": task_id,
                        "message": f"已取消订阅任务 {task_id} 的通知"
                    }
                },
                connection_id
            )
            logger.info(f"连接 {connection_id} 取消订阅任务 {task_id}")
        else:
            await ws_manager.send_personal_message(
                {
                    "type": "error",
                    "message": "缺少task_id参数"
                },
                connection_id
            )
            
    elif message_type == "get_status":
        # 获取连接状态
        await ws_manager.send_personal_message(
            {
                "type": "status",
                "data": {
                    "connection_id": connection_id,
                    "connected": True,
                    "subscribed_tasks": list(ws_manager.connection_tasks.get(connection_id, set()))
                }
            },
            connection_id
        )
        
    else:
        # 未知消息类型
        await ws_manager.send_personal_message(
            {
                "type": "error",
                "message": f"未知的消息类型: {message_type}"
            },
            connection_id
        )
