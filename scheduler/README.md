# 调度层 (Scheduler) - 开发任务文档

## 📋 模块概述

### 职责范围
调度层是整个系统的核心协调组件，负责：
- 🎯 **任务管理**：创建、跟踪、更新任务状态
- 🎯 **工作流编排**：协调各服务间的调用
- 🎯 **用户交互**：处理确认请求和响应
- 🎯 **错误处理**：重试机制和异常恢复
- 🎯 **通知服务**：WebSocket实时通知

### 技术栈
- **框架**：FastAPI
- **数据库**：SQLite
- **异步处理**：asyncio + 任务队列
- **实时通信**：WebSocket

## 🏗️ 目录结构

```
scheduler/
├── app.py                      # FastAPI应用主文件
├── config/
│   ├── __init__.py
│   ├── settings.py             # 配置管理
│   └── database.py             # 数据库配置
├── models/
│   ├── __init__.py
│   ├── task.py                 # 任务数据模型
│   ├── processing_result.py    # 处理结果模型
│   └── upload_record.py        # 上传记录模型
├── services/
│   ├── __init__.py
│   ├── task_manager.py         # 任务管理器
│   ├── workflow_orchestrator.py # 工作流编排器
│   ├── notification_service.py # 通知服务
│   └── external_api.py         # 外部API调用
├── api/
│   ├── __init__.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── tasks.py            # 任务相关API
│   │   ├── system.py           # 系统状态API
│   │   └── websocket.py        # WebSocket处理
│   └── schemas/
│       ├── __init__.py
│       ├── task_schemas.py     # 任务请求响应模型
│       └── system_schemas.py   # 系统状态模型
├── database/
│   ├── __init__.py
│   ├── init_db.py              # 数据库初始化
│   └── migrations/             # 数据库迁移
├── utils/
│   ├── __init__.py
│   ├── logger.py               # 日志工具
│   └── helpers.py              # 辅助函数
├── tests/
│   ├── __init__.py
│   ├── test_task_manager.py
│   ├── test_workflow.py
│   └── test_api.py
├── requirements.txt            # 依赖包
└── README.md                   # 本文档
```

## 📝 开发任务列表

### 1. 项目搭建（预计2天）

#### 1.1 基础环境搭建
- [ ] ⏳ 创建虚拟环境
- [ ] ⏳ 安装FastAPI及相关依赖
- [ ] ⏳ 配置开发环境
- [ ] ⏳ 设置代码格式化工具

#### 1.2 项目结构创建
- [ ] ⏳ 创建目录结构
- [ ] ⏳ 初始化各模块文件
- [ ] ⏳ 配置导入路径
- [ ] ⏳ 设置日志系统

### 2. 数据库设计（预计1天）

#### 2.1 数据模型设计
- [ ] ⏳ 设计任务表结构
- [ ] ⏳ 设计处理结果表结构
- [ ] ⏳ 设计上传记录表结构
- [ ] ⏳ 设计系统配置表结构

#### 2.2 数据库初始化
- [ ] ⏳ 编写SQLite初始化脚本
- [ ] ⏳ 创建数据模型类
- [ ] ⏳ 实现数据库连接管理
- [ ] ⏳ 编写数据库操作基类

### 3. 核心服务开发（预计4天）

#### 3.1 任务管理器开发
- [ ] ⏳ 实现任务创建功能
- [ ] ⏳ 实现任务状态更新
- [ ] ⏳ 实现任务查询功能
- [ ] ⏳ 实现任务生命周期管理
- [ ] ⏳ 添加任务重试机制

#### 3.2 工作流编排器开发
- [ ] ⏳ 设计工作流状态机
- [ ] ⏳ 实现视频分析流程编排
- [ ] ⏳ 实现用户确认流程编排
- [ ] ⏳ 实现上传流程编排
- [ ] ⏳ 添加流程错误处理

#### 3.3 通知服务开发
- [ ] ⏳ 实现WebSocket连接管理
- [ ] ⏳ 实现实时消息推送
- [ ] ⏳ 实现任务状态通知
- [ ] ⏳ 实现用户确认通知
- [ ] ⏳ 添加通知失败重试

### 4. API接口开发（预计3天）

#### 4.1 任务相关API
- [ ] ⏳ POST /api/v1/recording/completed
- [ ] ⏳ GET /api/v1/tasks/{task_id}/status
- [ ] ⏳ GET /api/v1/tasks/pending
- [ ] ⏳ POST /api/v1/tasks/{task_id}/confirm
- [ ] ⏳ DELETE /api/v1/tasks/{task_id}

#### 4.2 系统状态API
- [ ] ⏳ GET /api/v1/system/status
- [ ] ⏳ GET /api/v1/system/config
- [ ] ⏳ POST /api/v1/system/config
- [ ] ⏳ GET /api/v1/system/health

#### 4.3 WebSocket接口
- [ ] ⏳ 实现WebSocket连接处理
- [ ] ⏳ 实现消息广播机制
- [ ] ⏳ 实现客户端认证
- [ ] ⏳ 添加连接状态管理

### 5. 外部服务集成（预计2天）

#### 5.1 视频分析服务集成
- [ ] ⏳ 实现分析任务提交
- [ ] ⏳ 实现分析状态查询
- [ ] ⏳ 实现分析结果获取
- [ ] ⏳ 添加服务健康检查

#### 5.2 上传服务集成
- [ ] ⏳ 实现上传任务提交
- [ ] ⏳ 实现上传状态查询
- [ ] ⏳ 实现上传结果处理
- [ ] ⏳ 添加上传失败重试

### 6. 错误处理和测试（预计2天）

#### 6.1 错误处理机制
- [ ] ⏳ 实现全局异常处理
- [ ] ⏳ 添加业务异常定义
- [ ] ⏳ 实现错误日志记录
- [ ] ⏳ 添加错误恢复机制

#### 6.2 单元测试
- [ ] ⏳ 编写任务管理器测试
- [ ] ⏳ 编写工作流编排器测试
- [ ] ⏳ 编写API接口测试
- [ ] ⏳ 编写集成测试

## 🔧 技术实现要点

### 1. 数据库设计
```sql
-- 任务表
CREATE TABLE tasks (
    id VARCHAR(36) PRIMARY KEY,
    video_path VARCHAR(500) NOT NULL,
    streamer VARCHAR(100),
    platform VARCHAR(50),
    status VARCHAR(50) DEFAULT 'created',
    progress INTEGER DEFAULT 0,
    current_step VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    metadata TEXT  -- JSON格式
);
```

### 2. API接口规范
```python
# 录制完成通知
POST /api/v1/recording/completed
{
    "video_path": "/path/to/video.mp4",
    "duration": 600,
    "streamer": "主播名称",
    "platform": "douyin",
    "timestamp": "2024-01-20T10:30:00Z",
    "file_size": 1024000000,
    "metadata": {...}
}
```

### 3. WebSocket事件
```python
# 前端监听的事件
ws_events = {
    'task.created': '新任务创建',
    'task.status_updated': '任务状态更新',
    'task.analysis_completed': '分析完成需确认',
    'task.upload_completed': '上传完成',
    'system.status_updated': '系统状态更新'
}
```

### 4. 工作流状态机
```python
# 任务状态流转
TASK_STATES = {
    'created': '已创建',
    'analyzing': '分析中',
    'waiting_confirm': '等待确认',
    'generating': '生成中',
    'uploading': '上传中',
    'completed': '已完成',
    'failed': '失败',
    'cancelled': '已取消'
}
```

## 📊 性能要求

### 1. 响应时间
- API响应时间 < 200ms
- WebSocket消息延迟 < 100ms
- 数据库查询时间 < 50ms

### 2. 并发处理
- 支持同时处理10个任务
- WebSocket连接数 > 100
- 数据库连接池 = 20

### 3. 可靠性
- 系统可用性 > 99%
- 任务成功率 > 95%
- 数据一致性保证

## 🧪 测试策略

### 1. 单元测试
- 覆盖率 > 80%
- 核心业务逻辑100%覆盖
- 异常场景测试

### 2. 集成测试
- API接口测试
- 数据库操作测试
- 外部服务集成测试

### 3. 性能测试
- 压力测试
- 并发测试
- 内存泄漏测试

## 📝 开发注意事项

### 1. 代码规范
- 使用Type Hints
- 遵循PEP 8规范
- 添加详细注释
- 编写清晰的文档字符串

### 2. 安全考虑
- API认证和授权
- 输入数据验证
- SQL注入防护
- 敏感信息加密

### 3. 监控和日志
- 详细的操作日志
- 性能监控指标
- 错误告警机制
- 健康检查接口

---

**模块负责人**：开发者  
**预计开发周期**：1.5周  
**依赖模块**：无  
**被依赖模块**：视频分析服务、上传服务、前端界面
