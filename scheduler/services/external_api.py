"""
外部API服务
"""
import aiohttp
import asyncio
from typing import Dict, Any, Optional, List
from config.settings import settings
from utils.logger import get_logger

logger = get_logger(__name__)


class ExternalAPIService:
    """外部API服务"""
    
    def __init__(self):
        self.video_analysis_url = settings.video_analysis_url
        self.upload_service_url = settings.upload_service_url
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def start_video_analysis(
        self, 
        task_id: str, 
        video_path: str, 
        metadata: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """启动视频分析"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {
                    "task_id": task_id,
                    "video_path": video_path,
                    "metadata": metadata
                }
                
                async with session.post(
                    f"{self.video_analysis_url}/api/v1/analysis/start",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"视频分析启动成功: {task_id}")
                        return result
                    else:
                        error_text = await response.text()
                        logger.error(f"视频分析启动失败: {task_id}, 状态码: {response.status}, 错误: {error_text}")
                        return None
                        
        except asyncio.TimeoutError:
            logger.error(f"视频分析请求超时: {task_id}")
            return None
        except Exception as e:
            logger.error(f"视频分析请求异常: {task_id}, 错误: {e}")
            return None
    
    async def get_analysis_result(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取分析结果"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                async with session.get(
                    f"{self.video_analysis_url}/api/v1/analysis/{task_id}/result"
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"获取分析结果成功: {task_id}")
                        return result
                    else:
                        logger.error(f"获取分析结果失败: {task_id}, 状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"获取分析结果异常: {task_id}, 错误: {e}")
            return None
    
    async def save_user_confirmation(
        self, 
        task_id: str, 
        confirmation_data: Dict[str, Any]
    ) -> bool:
        """保存用户确认数据"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {
                    "task_id": task_id,
                    **confirmation_data
                }
                
                async with session.post(
                    f"{self.video_analysis_url}/api/v1/analysis/{task_id}/confirm",
                    json=payload
                ) as response:
                    if response.status == 200:
                        logger.info(f"保存用户确认成功: {task_id}")
                        return True
                    else:
                        logger.error(f"保存用户确认失败: {task_id}, 状态码: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"保存用户确认异常: {task_id}, 错误: {e}")
            return False
    
    async def start_video_generation(self, task_id: str) -> Optional[Dict[str, Any]]:
        """启动视频生成"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {"task_id": task_id}
                
                async with session.post(
                    f"{self.video_analysis_url}/api/v1/generation/start",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"视频生成启动成功: {task_id}")
                        return result
                    else:
                        logger.error(f"视频生成启动失败: {task_id}, 状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"视频生成请求异常: {task_id}, 错误: {e}")
            return None
    
    async def start_video_upload(self, task_id: str) -> Optional[Dict[str, Any]]:
        """启动视频上传"""
        try:
            async with aiohttp.ClientSession(timeout=self.timeout) as session:
                payload = {"task_id": task_id}
                
                async with session.post(
                    f"{self.upload_service_url}/api/v1/upload/start",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"视频上传启动成功: {task_id}")
                        return result
                    else:
                        logger.error(f"视频上传启动失败: {task_id}, 状态码: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"视频上传请求异常: {task_id}, 错误: {e}")
            return None
    
    async def check_service_health(self, service_url: str) -> bool:
        """检查服务健康状态"""
        try:
            async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=5)) as session:
                async with session.get(f"{service_url}/health") as response:
                    return response.status == 200
        except Exception:
            return False
    
    async def get_all_services_health(self) -> Dict[str, bool]:
        """获取所有服务健康状态"""
        services = {
            "video_analysis": self.video_analysis_url,
            "upload_service": self.upload_service_url
        }
        
        health_status = {}
        for service_name, service_url in services.items():
            health_status[service_name] = await self.check_service_health(service_url)
        
        return health_status


# 全局外部API服务实例
external_api = ExternalAPIService()
