"""
工作流编排器服务
"""
import asyncio
from typing import Dict, Any, Optional
from models.task import Task, TaskStatus, TaskStep
from utils.logger import get_logger

logger = get_logger(__name__)


class WorkflowOrchestrator:
    """工作流编排器"""
    
    def __init__(self):
        self._running_workflows: Dict[str, asyncio.Task] = {}
    
    async def start_workflow(self, task_id: str) -> bool:
        """启动工作流"""
        if task_id in self._running_workflows:
            logger.warning(f"工作流已在运行: {task_id}")
            return False
        
        # 创建工作流任务
        workflow_task = asyncio.create_task(self._execute_workflow(task_id))
        self._running_workflows[task_id] = workflow_task
        
        logger.info(f"启动工作流: {task_id}")
        return True
    
    async def stop_workflow(self, task_id: str) -> bool:
        """停止工作流"""
        if task_id not in self._running_workflows:
            return False
        
        workflow_task = self._running_workflows[task_id]
        workflow_task.cancel()
        
        try:
            await workflow_task
        except asyncio.CancelledError:
            pass
        
        del self._running_workflows[task_id]
        
        # 更新任务状态为已取消
        await task_manager.update_task_status(task_id, TaskStatus.CANCELLED)
        
        logger.info(f"停止工作流: {task_id}")
        return True
    
    async def _execute_workflow(self, task_id: str):
        """执行工作流"""
        try:
            # 延迟导入避免循环导入
            from services.task_manager import task_manager
            from services.external_api import external_api
            from services.notification_service import notification_service

            # 获取任务
            task = await task_manager.get_task(task_id)
            if not task:
                logger.error(f"任务不存在: {task_id}")
                return
            
            # 步骤1: 视频分析
            await self._step_video_analysis(task_id)
            
            # 步骤2: 等待用户确认
            await self._step_wait_user_confirmation(task_id)
            
            # 步骤3: 视频生成
            await self._step_video_generation(task_id)
            
            # 步骤4: 视频上传
            await self._step_video_upload(task_id)
            
            # 完成工作流
            await task_manager.update_task_status(
                task_id, 
                TaskStatus.COMPLETED, 
                progress=100,
                current_step=TaskStep.COMPLETED
            )
            
            # 发送完成通知
            await notification_service.send_task_notification(
                task_id, 
                "task.completed", 
                {"message": "任务已完成"}
            )
            
            logger.info(f"工作流完成: {task_id}")
            
        except asyncio.CancelledError:
            logger.info(f"工作流被取消: {task_id}")
            raise
        except Exception as e:
            logger.error(f"工作流执行失败: {task_id}, 错误: {e}")
            await task_manager.update_task_status(task_id, TaskStatus.FAILED)
            await notification_service.send_task_notification(
                task_id, 
                "task.failed", 
                {"error": str(e)}
            )
        finally:
            # 清理运行中的工作流
            self._running_workflows.pop(task_id, None)
    
    async def _step_video_analysis(self, task_id: str):
        """步骤1: 视频分析"""
        logger.info(f"开始视频分析: {task_id}")
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id, 
            TaskStatus.ANALYZING, 
            progress=10,
            current_step=TaskStep.VIDEO_ANALYSIS
        )
        
        # 发送通知
        await notification_service.send_task_notification(
            task_id, 
            "task.analysis_started", 
            {"message": "开始视频分析"}
        )
        
        # 调用视频分析服务
        task = await task_manager.get_task(task_id)
        analysis_result = await external_api.start_video_analysis(
            task_id, 
            task.video_path,
            task.metadata or {}
        )
        
        if not analysis_result:
            raise Exception("视频分析失败")
        
        # 更新进度
        await task_manager.update_task_status(task_id, TaskStatus.ANALYZING, progress=50)
        
        logger.info(f"视频分析完成: {task_id}")
    
    async def _step_wait_user_confirmation(self, task_id: str):
        """步骤2: 等待用户确认"""
        logger.info(f"等待用户确认: {task_id}")
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id, 
            TaskStatus.WAITING_CONFIRM, 
            progress=60,
            current_step=TaskStep.USER_CONFIRMATION
        )
        
        # 发送确认通知
        await notification_service.send_task_notification(
            task_id, 
            "task.analysis_completed", 
            {"message": "分析完成，请确认选择"}
        )
        
        # 等待用户确认（通过外部API调用来完成）
        # 这里只是更新状态，实际确认通过API接口处理
        logger.info(f"等待用户确认中: {task_id}")
    
    async def _step_video_generation(self, task_id: str):
        """步骤3: 视频生成"""
        logger.info(f"开始视频生成: {task_id}")
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id, 
            TaskStatus.GENERATING, 
            progress=70,
            current_step=TaskStep.VIDEO_GENERATION
        )
        
        # 发送通知
        await notification_service.send_task_notification(
            task_id, 
            "task.generation_started", 
            {"message": "开始视频生成"}
        )
        
        # 调用视频分析服务进行生成
        generation_result = await external_api.start_video_generation(task_id)
        
        if not generation_result:
            raise Exception("视频生成失败")
        
        # 更新进度
        await task_manager.update_task_status(task_id, TaskStatus.GENERATING, progress=90)
        
        logger.info(f"视频生成完成: {task_id}")
    
    async def _step_video_upload(self, task_id: str):
        """步骤4: 视频上传"""
        logger.info(f"开始视频上传: {task_id}")
        
        # 更新任务状态
        await task_manager.update_task_status(
            task_id, 
            TaskStatus.UPLOADING, 
            progress=95,
            current_step=TaskStep.VIDEO_UPLOAD
        )
        
        # 发送通知
        await notification_service.send_task_notification(
            task_id, 
            "task.upload_started", 
            {"message": "开始视频上传"}
        )
        
        # 调用上传服务
        upload_result = await external_api.start_video_upload(task_id)
        
        if not upload_result:
            raise Exception("视频上传失败")
        
        logger.info(f"视频上传完成: {task_id}")
    
    async def handle_user_confirmation(
        self, 
        task_id: str, 
        confirmation_data: Dict[str, Any]
    ) -> bool:
        """处理用户确认"""
        try:
            # 检查任务状态
            task = await task_manager.get_task(task_id)
            if not task or task.status != TaskStatus.WAITING_CONFIRM:
                logger.warning(f"任务状态不正确，无法处理确认: {task_id}")
                return False
            
            # 保存用户选择
            await external_api.save_user_confirmation(task_id, confirmation_data)
            
            # 继续工作流（如果工作流还在运行）
            if task_id in self._running_workflows:
                # 工作流会自动继续到下一步
                pass
            else:
                # 重新启动工作流从生成步骤开始
                await self._step_video_generation(task_id)
                await self._step_video_upload(task_id)
                
                # 完成任务
                await task_manager.update_task_status(
                    task_id, 
                    TaskStatus.COMPLETED, 
                    progress=100,
                    current_step=TaskStep.COMPLETED
                )
            
            logger.info(f"处理用户确认完成: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"处理用户确认失败: {task_id}, 错误: {e}")
            return False
    
    def get_running_workflows(self) -> Dict[str, str]:
        """获取运行中的工作流"""
        return {task_id: "running" for task_id in self._running_workflows.keys()}


# 全局工作流编排器实例
workflow_orchestrator = WorkflowOrchestrator()
