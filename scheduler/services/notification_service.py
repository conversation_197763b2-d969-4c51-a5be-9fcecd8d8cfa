"""
通知服务
"""
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, List, Optional, Set
from fastapi import WebSocket
from utils.logger import get_logger

logger = get_logger(__name__)


class WebSocketManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_tasks: Dict[str, Set[str]] = {}  # 连接ID -> 订阅的任务ID集合
    
    async def connect(self, websocket: WebSocket, connection_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        self.connection_tasks[connection_id] = set()
        logger.info(f"WebSocket连接建立: {connection_id}")
    
    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        self.active_connections.pop(connection_id, None)
        self.connection_tasks.pop(connection_id, None)
        logger.info(f"WebSocket连接断开: {connection_id}")
    
    async def send_personal_message(self, message: Dict[str, Any], connection_id: str):
        """发送个人消息"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                logger.error(f"发送个人消息失败: {connection_id}, 错误: {e}")
                self.disconnect(connection_id)
    
    async def broadcast(self, message: Dict[str, Any]):
        """广播消息给所有连接"""
        if not self.active_connections:
            return
        
        message_text = json.dumps(message, ensure_ascii=False)
        disconnected = []
        
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(message_text)
            except Exception as e:
                logger.error(f"广播消息失败: {connection_id}, 错误: {e}")
                disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)
    
    async def send_to_task_subscribers(self, task_id: str, message: Dict[str, Any]):
        """发送消息给订阅特定任务的连接"""
        message_text = json.dumps(message, ensure_ascii=False)
        disconnected = []
        
        for connection_id, subscribed_tasks in self.connection_tasks.items():
            if task_id in subscribed_tasks:
                websocket = self.active_connections.get(connection_id)
                if websocket:
                    try:
                        await websocket.send_text(message_text)
                    except Exception as e:
                        logger.error(f"发送任务消息失败: {connection_id}, 错误: {e}")
                        disconnected.append(connection_id)
        
        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)
    
    def subscribe_task(self, connection_id: str, task_id: str):
        """订阅任务通知"""
        if connection_id in self.connection_tasks:
            self.connection_tasks[connection_id].add(task_id)
            logger.info(f"连接 {connection_id} 订阅任务 {task_id}")
    
    def unsubscribe_task(self, connection_id: str, task_id: str):
        """取消订阅任务通知"""
        if connection_id in self.connection_tasks:
            self.connection_tasks[connection_id].discard(task_id)
            logger.info(f"连接 {connection_id} 取消订阅任务 {task_id}")
    
    def get_connection_count(self) -> int:
        """获取连接数量"""
        return len(self.active_connections)


class NotificationService:
    """通知服务"""
    
    def __init__(self):
        self.websocket_manager = WebSocketManager()
        self.notification_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = False
    
    async def start(self):
        """启动通知服务"""
        if self.is_running:
            return
        
        self.is_running = True
        # 启动通知处理任务
        asyncio.create_task(self._process_notifications())
        logger.info("通知服务已启动")
    
    async def stop(self):
        """停止通知服务"""
        self.is_running = False
        logger.info("通知服务已停止")
    
    async def _process_notifications(self):
        """处理通知队列"""
        while self.is_running:
            try:
                # 等待通知
                notification = await asyncio.wait_for(
                    self.notification_queue.get(), 
                    timeout=1.0
                )
                
                # 处理通知
                await self._handle_notification(notification)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"处理通知异常: {e}")
    
    async def _handle_notification(self, notification: Dict[str, Any]):
        """处理单个通知"""
        notification_type = notification.get("type")
        
        if notification_type == "task_notification":
            await self._handle_task_notification(notification)
        elif notification_type == "system_notification":
            await self._handle_system_notification(notification)
        elif notification_type == "broadcast":
            await self._handle_broadcast_notification(notification)
    
    async def _handle_task_notification(self, notification: Dict[str, Any]):
        """处理任务通知"""
        task_id = notification.get("task_id")
        if task_id:
            await self.websocket_manager.send_to_task_subscribers(task_id, notification)
    
    async def _handle_system_notification(self, notification: Dict[str, Any]):
        """处理系统通知"""
        await self.websocket_manager.broadcast(notification)
    
    async def _handle_broadcast_notification(self, notification: Dict[str, Any]):
        """处理广播通知"""
        await self.websocket_manager.broadcast(notification)
    
    async def send_task_notification(
        self, 
        task_id: str, 
        event: str, 
        data: Dict[str, Any]
    ):
        """发送任务通知"""
        notification = {
            "type": "task_notification",
            "task_id": task_id,
            "event": event,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.notification_queue.put(notification)
    
    async def send_system_notification(self, event: str, data: Dict[str, Any]):
        """发送系统通知"""
        notification = {
            "type": "system_notification",
            "event": event,
            "data": data,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.notification_queue.put(notification)
    
    async def broadcast_message(self, message: str, data: Optional[Dict[str, Any]] = None):
        """广播消息"""
        notification = {
            "type": "broadcast",
            "message": message,
            "data": data or {},
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.notification_queue.put(notification)
    
    def get_websocket_manager(self) -> WebSocketManager:
        """获取WebSocket管理器"""
        return self.websocket_manager


# 全局通知服务实例
notification_service = NotificationService()
