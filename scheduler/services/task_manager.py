"""
任务管理器服务
"""
import asyncio
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload

from models.task import Task, TaskStatus, TaskStep
from models.processing_result import ProcessingResult
from models.upload_record import UploadRecord
from config.database import AsyncSessionLocal
from utils.logger import get_logger
from utils.helpers import generate_task_id, validate_video_path, extract_streamer_from_path

logger = get_logger(__name__)


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self._active_tasks: Dict[str, Task] = {}
        self._task_locks: Dict[str, asyncio.Lock] = {}
    
    async def create_task(
        self,
        video_path: str,
        streamer: Optional[str] = None,
        platform: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Task:
        """创建新任务"""
        # 验证视频路径
        if not validate_video_path(video_path):
            raise ValueError(f"无效的视频路径: {video_path}")
        
        # 自动提取主播名称
        if not streamer:
            streamer = extract_streamer_from_path(video_path)
        
        # 创建任务对象
        task = Task(
            id=generate_task_id(),
            video_path=video_path,
            streamer=streamer,
            platform=platform,
            status=TaskStatus.CREATED,
            current_step=TaskStep.CREATED,
            task_metadata=metadata or {}
        )
        
        # 保存到数据库
        async with AsyncSessionLocal() as session:
            session.add(task)
            await session.commit()
            await session.refresh(task)
        
        # 添加到活跃任务
        self._active_tasks[task.id] = task
        self._task_locks[task.id] = asyncio.Lock()
        
        logger.info(f"创建任务: {task.id}, 视频: {video_path}")
        return task
    
    async def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        # 先从内存中查找
        if task_id in self._active_tasks:
            return self._active_tasks[task_id]
        
        # 从数据库查找
        async with AsyncSessionLocal() as session:
            result = await session.execute(
                select(Task).where(Task.id == task_id)
            )
            task = result.scalar_one_or_none()
            
            if task and task.status not in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                self._active_tasks[task_id] = task
                self._task_locks[task_id] = asyncio.Lock()
            
            return task
    
    async def update_task_status(
        self,
        task_id: str,
        status: str,
        progress: Optional[int] = None,
        current_step: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """更新任务状态"""
        async with self._get_task_lock(task_id):
            async with AsyncSessionLocal() as session:
                # 构建更新数据
                update_data = {
                    "status": status,
                    "updated_at": datetime.utcnow()
                }
                
                if progress is not None:
                    update_data["progress"] = progress
                
                if current_step is not None:
                    update_data["current_step"] = current_step
                
                if metadata is not None:
                    update_data["task_metadata"] = metadata
                
                # 如果任务完成，设置完成时间
                if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                    update_data["completed_at"] = datetime.utcnow()
                
                # 执行更新
                result = await session.execute(
                    update(Task)
                    .where(Task.id == task_id)
                    .values(**update_data)
                )
                
                await session.commit()
                
                # 更新内存中的任务
                if task_id in self._active_tasks:
                    task = self._active_tasks[task_id]
                    for key, value in update_data.items():
                        setattr(task, key, value)
                    
                    # 如果任务完成，从活跃任务中移除
                    if status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                        self._remove_active_task(task_id)
                
                logger.info(f"更新任务状态: {task_id}, 状态: {status}, 进度: {progress}")
                return result.rowcount > 0
    
    async def get_tasks(
        self,
        status: Optional[str] = None,
        platform: Optional[str] = None,
        limit: int = 50,
        offset: int = 0
    ) -> List[Task]:
        """获取任务列表"""
        async with AsyncSessionLocal() as session:
            query = select(Task)
            
            if status:
                query = query.where(Task.status == status)
            
            if platform:
                query = query.where(Task.platform == platform)
            
            query = query.order_by(Task.created_at.desc()).limit(limit).offset(offset)
            
            result = await session.execute(query)
            return result.scalars().all()
    
    async def get_active_tasks(self) -> List[Task]:
        """获取活跃任务列表"""
        return list(self._active_tasks.values())
    
    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        async with AsyncSessionLocal() as session:
            # 删除相关的处理结果和上传记录
            await session.execute(delete(ProcessingResult).where(ProcessingResult.task_id == task_id))
            await session.execute(delete(UploadRecord).where(UploadRecord.task_id == task_id))
            
            # 删除任务
            result = await session.execute(delete(Task).where(Task.id == task_id))
            await session.commit()
            
            # 从活跃任务中移除
            self._remove_active_task(task_id)
            
            logger.info(f"删除任务: {task_id}")
            return result.rowcount > 0
    
    async def cleanup_old_tasks(self, days: int = 30) -> int:
        """清理旧任务"""
        cutoff_date = datetime.utcnow() - timedelta(days=days)
        
        async with AsyncSessionLocal() as session:
            # 查找要删除的任务
            result = await session.execute(
                select(Task.id).where(
                    Task.completed_at < cutoff_date,
                    Task.status.in_([TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED])
                )
            )
            task_ids = [row[0] for row in result.fetchall()]
            
            if not task_ids:
                return 0
            
            # 删除相关记录
            await session.execute(delete(ProcessingResult).where(ProcessingResult.task_id.in_(task_ids)))
            await session.execute(delete(UploadRecord).where(UploadRecord.task_id.in_(task_ids)))
            await session.execute(delete(Task).where(Task.id.in_(task_ids)))
            
            await session.commit()
            
            logger.info(f"清理了 {len(task_ids)} 个旧任务")
            return len(task_ids)
    
    def _get_task_lock(self, task_id: str) -> asyncio.Lock:
        """获取任务锁"""
        if task_id not in self._task_locks:
            self._task_locks[task_id] = asyncio.Lock()
        return self._task_locks[task_id]
    
    def _remove_active_task(self, task_id: str):
        """从活跃任务中移除"""
        self._active_tasks.pop(task_id, None)
        self._task_locks.pop(task_id, None)


# 全局任务管理器实例
task_manager = TaskManager()
