"""
调度层服务启动脚本
"""
import sys
import os
import asyncio
import uvicorn
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入应用
from app import app
from config.settings import settings
from utils.logger import setup_logging, get_logger

# 设置日志
setup_logging()
logger = get_logger(__name__)


def main():
    """主函数"""
    logger.info("启动调度层服务...")
    
    # 启动服务器
    uvicorn.run(
        "app:app",
        host=settings.host,
        port=settings.port,
        reload=False,  # 禁用reload避免导入问题
        log_level=settings.log_level.lower()
    )


if __name__ == "__main__":
    main()
