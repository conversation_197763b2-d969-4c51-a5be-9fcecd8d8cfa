# 上传服务改造 - 开发任务文档

## 📋 模块概述

### 改造目标
将现有的auto_upload上传脚本包装成HTTP API服务，使其能够接收调度层的上传请求，并提供上传状态查询功能。

### 改造原则
- ✅ **API包装**：保持现有上传逻辑不变，只添加API层
- ✅ **异步处理**：支持异步上传，避免阻塞API响应
- ✅ **状态管理**：提供上传状态查询和进度跟踪
- ✅ **错误处理**：完善的错误处理和重试机制

### 技术要求
- **Web框架**：FastAPI
- **异步处理**：asyncio + threading
- **状态存储**：内存存储（后期可扩展为数据库）
- **现有依赖**：保持现有的Playwright等依赖

## 🔍 现有代码分析

### 关键文件分析
- **uploader.py**：核心上传逻辑（1598行）
- **models.py**：数据模型定义
- **example.py**：使用示例
- **config/**：配置文件

### 核心功能分析
- **上传器类**：Uploader类包含完整上传逻辑
- **数据模型**：UploadTask等数据结构
- **平台支持**：主要支持快手平台
- **错误处理**：已有基础的错误处理机制

## 📝 改造任务列表

### 1. 项目结构分析（预计0.5天）

#### 1.1 深入分析现有代码
- [ ] ⏳ 详细阅读uploader.py核心逻辑
- [ ] ⏳ 分析models.py数据结构
- [ ] ⏳ 理解现有的配置机制
- [ ] ⏳ 分析错误处理流程

#### 1.2 设计API包装方案
- [ ] ⏳ 设计API接口规范
- [ ] ⏳ 设计异步处理架构
- [ ] ⏳ 设计状态管理机制
- [ ] ⏳ 制定测试方案

### 2. FastAPI项目搭建（预计0.5天）

#### 2.1 创建API服务器
- [ ] ⏳ 创建api_server.py主文件
- [ ] ⏳ 安装FastAPI相关依赖
- [ ] ⏳ 配置CORS和中间件
- [ ] ⏳ 设置日志系统

#### 2.2 项目结构调整
- [ ] ⏳ 创建api目录结构
- [ ] ⏳ 重构导入路径
- [ ] ⏳ 配置环境变量
- [ ] ⏳ 更新requirements.txt

### 3. 核心API开发（预计1.5天）

#### 3.1 上传任务API
- [ ] ⏳ POST /api/v1/upload/start - 开始上传
- [ ] ⏳ GET /api/v1/upload/status/{task_id} - 查询状态
- [ ] ⏳ DELETE /api/v1/upload/{task_id} - 取消上传
- [ ] ⏳ GET /api/v1/upload/history - 上传历史

#### 3.2 系统管理API
- [ ] ⏳ GET /api/v1/system/health - 健康检查
- [ ] ⏳ GET /api/v1/system/config - 配置查询
- [ ] ⏳ POST /api/v1/system/config - 配置更新
- [ ] ⏳ GET /api/v1/system/stats - 统计信息

#### 3.3 请求响应模型
- [ ] ⏳ 定义上传请求模型
- [ ] ⏳ 定义状态响应模型
- [ ] ⏳ 定义错误响应模型
- [ ] ⏳ 添加数据验证规则

### 4. 异步处理机制（预计1天）

#### 4.1 任务管理器
- [ ] ⏳ 实现任务状态管理
- [ ] ⏳ 实现任务队列机制
- [ ] ⏳ 实现并发控制
- [ ] ⏳ 实现任务清理机制

#### 4.2 异步上传处理
- [ ] ⏳ 包装现有上传器为异步
- [ ] ⏳ 实现进度回调机制
- [ ] ⏳ 实现状态更新通知
- [ ] ⏳ 实现错误处理和重试

#### 4.3 调度层通知
- [ ] ⏳ 实现上传完成通知
- [ ] ⏳ 实现上传失败通知
- [ ] ⏳ 实现进度更新通知
- [ ] ⏳ 添加通知重试机制

### 5. 集成测试（预计0.5天）

#### 5.1 API功能测试
- [ ] ⏳ 测试上传任务创建
- [ ] ⏳ 测试状态查询功能
- [ ] ⏳ 测试错误处理机制
- [ ] ⏳ 测试并发上传

#### 5.2 与调度层集成测试
- [ ] ⏳ 测试接收调度层请求
- [ ] ⏳ 测试向调度层发送通知
- [ ] ⏳ 测试网络异常处理
- [ ] ⏳ 验证数据传输格式

## 🏗️ 新增目录结构

```
auto_upload/
├── api_server.py               # FastAPI服务器主文件
├── api/
│   ├── __init__.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── upload.py           # 上传相关API
│   │   └── system.py           # 系统管理API
│   └── schemas/
│       ├── __init__.py
│       ├── upload_schemas.py   # 上传请求响应模型
│       └── system_schemas.py   # 系统模型
├── services/
│   ├── __init__.py
│   ├── upload_service.py       # 上传服务封装
│   ├── task_manager.py         # 任务管理器
│   └── notification_service.py # 通知服务
├── utils/
│   ├── __init__.py
│   ├── async_wrapper.py        # 异步包装器
│   └── logger.py               # 日志工具
├── uploader.py                 # 原有上传器（保持不变）
├── models.py                   # 原有模型（保持不变）
├── example.py                  # 原有示例（保持不变）
└── requirements.txt            # 更新依赖
```

## 🔧 技术实现方案

### 1. FastAPI服务器主文件
```python
# api_server.py
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import asyncio
import threading
from typing import Dict, Any
import uuid
from datetime import datetime

from .services.upload_service import UploadService
from .services.task_manager import TaskManager
from .api.routes import upload, system

app = FastAPI(title="上传服务API", version="1.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 全局服务实例
task_manager = TaskManager()
upload_service = UploadService(task_manager)

# 包含路由
app.include_router(upload.router, prefix="/api/v1/upload", tags=["upload"])
app.include_router(system.router, prefix="/api/v1/system", tags=["system"])

@app.on_startup
async def startup_event():
    """启动时初始化"""
    await task_manager.initialize()
    await upload_service.initialize()

@app.on_shutdown
async def shutdown_event():
    """关闭时清理"""
    await task_manager.cleanup()
    await upload_service.cleanup()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
```

### 2. 上传API路由
```python
# api/routes/upload.py
from fastapi import APIRouter, HTTPException, BackgroundTasks
from ..schemas.upload_schemas import UploadRequest, UploadResponse, StatusResponse
from ...services.upload_service import upload_service
from ...services.task_manager import task_manager

router = APIRouter()

@router.post("/start", response_model=UploadResponse)
async def start_upload(request: UploadRequest, background_tasks: BackgroundTasks):
    """开始上传任务"""
    try:
        # 创建任务
        task_id = await task_manager.create_task(request.dict())
        
        # 后台执行上传
        background_tasks.add_task(upload_service.process_upload, task_id, request)
        
        return UploadResponse(
            task_id=task_id,
            status="started",
            message="上传任务已开始"
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/status/{task_id}", response_model=StatusResponse)
async def get_upload_status(task_id: str):
    """查询上传状态"""
    try:
        status = await task_manager.get_task_status(task_id)
        if not status:
            raise HTTPException(status_code=404, detail="任务不存在")
        
        return StatusResponse(**status)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@router.delete("/{task_id}")
async def cancel_upload(task_id: str):
    """取消上传任务"""
    try:
        success = await task_manager.cancel_task(task_id)
        if not success:
            raise HTTPException(status_code=404, detail="任务不存在或无法取消")
        
        return {"message": "任务已取消"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 3. 上传服务封装
```python
# services/upload_service.py
import asyncio
import threading
from typing import Dict, Any
from ..uploader import Uploader
from ..models import UploadTask
from .notification_service import NotificationService

class UploadService:
    def __init__(self, task_manager):
        self.task_manager = task_manager
        self.notification_service = NotificationService()
        self.executor = None
    
    async def initialize(self):
        """初始化服务"""
        self.executor = threading.ThreadPoolExecutor(max_workers=3)
    
    async def process_upload(self, task_id: str, request_data: Dict[str, Any]):
        """处理上传任务"""
        try:
            # 更新任务状态
            await self.task_manager.update_task_status(task_id, "processing")
            
            # 创建上传任务
            upload_task = UploadTask(
                video_path=request_data["video_path"],
                title=request_data["title"],
                description=request_data.get("description", ""),
                tags=request_data.get("tags", []),
                privacy=request_data.get("privacy", "public")
            )
            
            # 在线程池中执行上传
            loop = asyncio.get_event_loop()
            success = await loop.run_in_executor(
                self.executor,
                self._sync_upload,
                upload_task,
                task_id
            )
            
            # 更新最终状态
            final_status = "completed" if success else "failed"
            await self.task_manager.update_task_status(task_id, final_status)
            
            # 通知调度层
            await self.notification_service.notify_scheduler_upload_completed(
                task_id, success
            )
            
        except Exception as e:
            await self.task_manager.update_task_status(task_id, "failed", str(e))
            await self.notification_service.notify_scheduler_upload_completed(
                task_id, False, str(e)
            )
    
    def _sync_upload(self, upload_task: UploadTask, task_id: str) -> bool:
        """同步上传处理（在线程中执行）"""
        try:
            uploader = Uploader(upload_task)
            
            # 设置进度回调
            def progress_callback(progress: int):
                asyncio.create_task(
                    self.task_manager.update_task_progress(task_id, progress)
                )
            
            uploader.set_progress_callback(progress_callback)
            
            # 执行上传
            return uploader.upload()
            
        except Exception as e:
            print(f"上传异常: {e}")
            return False
```

### 4. 任务管理器
```python
# services/task_manager.py
import asyncio
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

class TaskManager:
    def __init__(self):
        self.tasks: Dict[str, Dict[str, Any]] = {}
        self.lock = asyncio.Lock()
    
    async def initialize(self):
        """初始化任务管理器"""
        pass
    
    async def create_task(self, task_data: Dict[str, Any]) -> str:
        """创建新任务"""
        async with self.lock:
            task_id = str(uuid.uuid4())
            self.tasks[task_id] = {
                "id": task_id,
                "status": "created",
                "progress": 0,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "data": task_data,
                "error": None
            }
            return task_id
    
    async def update_task_status(self, task_id: str, status: str, error: str = None):
        """更新任务状态"""
        async with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["status"] = status
                self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
                if error:
                    self.tasks[task_id]["error"] = error
    
    async def update_task_progress(self, task_id: str, progress: int):
        """更新任务进度"""
        async with self.lock:
            if task_id in self.tasks:
                self.tasks[task_id]["progress"] = progress
                self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
    
    async def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        async with self.lock:
            return self.tasks.get(task_id)
    
    async def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        async with self.lock:
            if task_id in self.tasks and self.tasks[task_id]["status"] in ["created", "processing"]:
                self.tasks[task_id]["status"] = "cancelled"
                self.tasks[task_id]["updated_at"] = datetime.now().isoformat()
                return True
            return False
```

### 5. 请求响应模型
```python
# api/schemas/upload_schemas.py
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime

class UploadRequest(BaseModel):
    video_path: str
    title: str
    description: Optional[str] = ""
    tags: Optional[List[str]] = []
    privacy: Optional[str] = "public"
    task_id: Optional[str] = None  # 来自调度层的任务ID

class UploadResponse(BaseModel):
    task_id: str
    status: str
    message: str

class StatusResponse(BaseModel):
    id: str
    status: str
    progress: int
    created_at: str
    updated_at: str
    error: Optional[str] = None
```

## 📊 API接口规范

### 1. 上传相关API
```
POST /api/v1/upload/start
Content-Type: application/json

{
    "video_path": "/path/to/video.mp4",
    "title": "视频标题",
    "description": "视频描述",
    "tags": ["标签1", "标签2"],
    "privacy": "public",
    "task_id": "来自调度层的任务ID"
}

Response:
{
    "task_id": "upload_task_uuid",
    "status": "started",
    "message": "上传任务已开始"
}
```

### 2. 状态查询API
```
GET /api/v1/upload/status/{task_id}

Response:
{
    "id": "task_id",
    "status": "processing|completed|failed|cancelled",
    "progress": 75,
    "created_at": "2024-01-20T10:30:00",
    "updated_at": "2024-01-20T10:35:00",
    "error": null
}
```

## 📝 注意事项

### 1. 兼容性保证
- **保持原有接口**：uploader.py等文件保持不变
- **独立运行**：API服务可选，原有脚本仍可独立使用
- **配置兼容**：支持原有配置文件格式

### 2. 性能考虑
- **并发控制**：限制同时上传的任务数量
- **资源管理**：及时清理完成的任务和临时文件
- **内存使用**：避免大量任务数据常驻内存

### 3. 错误处理
- **网络异常**：上传过程中的网络问题
- **文件异常**：视频文件不存在或损坏
- **平台异常**：快手平台的限制或错误
- **系统异常**：内存不足、磁盘空间等

---

**模块负责人**：开发者  
**预计改造周期**：1周  
**风险等级**：中  
**依赖模块**：调度层（需要接收调度层的上传请求）
