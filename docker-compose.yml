version: '3.8'

services:
  # 调度层服务
  scheduler:
    build: ./scheduler
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=sqlite:///./scheduler.db
    volumes:
      - ./data:/app/data
    depends_on:
      - redis
    restart: unless-stopped

  # 视频分析服务
  video-analysis:
    build: ./video_analysis
    ports:
      - "8000:8000"
    environment:
      - SCHEDULER_URL=http://scheduler:8080
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./videos:/app/videos
      - ./temp:/app/temp
    restart: unless-stopped

  # 上传服务
  upload-service:
    build: ./auto_upload
    ports:
      - "8001:8001"
    environment:
      - SCHEDULER_URL=http://scheduler:8080
    volumes:
      - ./videos:/app/videos
    restart: unless-stopped

  # 前端界面
  frontend:
    build: ./frontend
    ports:
      - "8888:80"
    environment:
      - API_BASE_URL=http://localhost:8080
    depends_on:
      - scheduler
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

volumes:
  data:
  videos:
  temp:
