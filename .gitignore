# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/

# 各模块的虚拟环境
scheduler/venv/
scheduler/env/
video_analysis/venv/
video_analysis/env/
auto_upload/venv/
auto_upload/env/
DouyinLiveRecorder-main/venv/
DouyinLiveRecorder-main/env/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
*.log
*.db
*.sqlite
*.sqlite3

# Temporary files
temp/
tmp/
*.tmp

# Video files
*.mp4
*.avi
*.mov
*.mkv
*.flv

# Audio files
*.mp3
*.wav
*.aac

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# 各模块的node_modules
frontend/node_modules/
*/node_modules/

# Frontend build
frontend/dist/
frontend/.nuxt/
frontend/.output/

# Environment variables
.env
.env.local
.env.*.local

# Docker
.dockerignore

# Logs
logs/
*.log

# Database
data/
*.db
*.sqlite

# 各模块的数据库文件
scheduler/data/
scheduler/*.db
scheduler/*.sqlite
video_analysis/data/
video_analysis/*.db
auto_upload/data/
auto_upload/*.db

# AI models cache
.cache/
models/
video_analysis/models/
video_analysis/.cache/

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version
