#!/usr/bin/env python3
"""
直播自动化处理系统 - 统一启动脚本
支持开发环境和生产环境的一键启动
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class ServiceManager:
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        
    def start_service(self, name, cmd, cwd, env=None):
        """启动单个服务"""
        print(f"🚀 启动 {name}...")
        
        full_env = os.environ.copy()
        if env:
            full_env.update(env)
            
        try:
            process = subprocess.Popen(
                cmd,
                cwd=cwd,
                env=full_env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                shell=True
            )
            self.processes.append((name, process))
            print(f"✅ {name} 启动成功 (PID: {process.pid})")
            return True
        except Exception as e:
            print(f"❌ {name} 启动失败: {e}")
            return False
    
    def check_service_health(self, url, timeout=30):
        """检查服务健康状态"""
        import requests
        for i in range(timeout):
            try:
                response = requests.get(f"{url}/health", timeout=2)
                if response.status_code == 200:
                    return True
            except:
                pass
            time.sleep(1)
        return False
    
    def start_development(self):
        """启动开发环境"""
        print("🔧 启动开发环境...")
        
        services = [
            {
                "name": "调度层",
                "cmd": "python app.py",
                "cwd": self.base_dir / "scheduler",
                "url": "http://localhost:8080"
            },
            {
                "name": "视频分析服务", 
                "cmd": "python app.py",
                "cwd": self.base_dir / "video_analysis",
                "url": "http://localhost:8000"
            },
            {
                "name": "上传服务",
                "cmd": "python api_server.py", 
                "cwd": self.base_dir / "auto_upload",
                "url": "http://localhost:8001"
            },
            {
                "name": "前端界面",
                "cmd": "npm run dev",
                "cwd": self.base_dir / "frontend", 
                "url": "http://localhost:8888"
            }
        ]
        
        # 启动所有服务
        for service in services:
            success = self.start_service(
                service["name"],
                service["cmd"], 
                service["cwd"]
            )
            if not success:
                print(f"❌ {service['name']} 启动失败，停止启动流程")
                self.stop_all()
                return False
            
            # 等待服务启动
            time.sleep(3)
        
        print("\n🎉 所有服务启动完成！")
        print("📊 服务状态:")
        print("  - 调度层: http://localhost:8080")
        print("  - 视频分析: http://localhost:8000") 
        print("  - 上传服务: http://localhost:8001")
        print("  - 前端界面: http://localhost:8888")
        print("\n按 Ctrl+C 停止所有服务")
        
        return True
    
    def start_production(self):
        """启动生产环境（Docker）"""
        print("🐳 启动生产环境 (Docker)...")
        
        if not self.check_docker():
            return False
            
        cmd = "docker-compose up -d"
        success = self.start_service("Docker Compose", cmd, self.base_dir)
        
        if success:
            print("🎉 生产环境启动完成！")
            print("📊 服务地址:")
            print("  - 前端界面: http://localhost:8888")
            print("  - API文档: http://localhost:8080/docs")
        
        return success
    
    def check_docker(self):
        """检查Docker环境"""
        try:
            subprocess.run(["docker", "--version"], check=True, capture_output=True)
            subprocess.run(["docker-compose", "--version"], check=True, capture_output=True)
            return True
        except:
            print("❌ Docker 或 Docker Compose 未安装")
            return False
    
    def stop_all(self):
        """停止所有服务"""
        print("\n🛑 停止所有服务...")
        for name, process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print(f"✅ {name} 已停止")
            except:
                try:
                    process.kill()
                    print(f"🔪 强制停止 {name}")
                except:
                    print(f"❌ 无法停止 {name}")
    
    def install_dependencies(self):
        """安装依赖"""
        print("📦 安装项目依赖...")
        
        # Python依赖
        python_services = ["scheduler", "video_analysis", "auto_upload"]
        for service in python_services:
            service_dir = self.base_dir / service
            if (service_dir / "requirements.txt").exists():
                print(f"📦 安装 {service} 依赖...")
                subprocess.run([
                    sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
                ], cwd=service_dir)
        
        # Node.js依赖
        frontend_dir = self.base_dir / "frontend"
        if (frontend_dir / "package.json").exists():
            print("📦 安装前端依赖...")
            subprocess.run(["npm", "install"], cwd=frontend_dir)
        
        print("✅ 依赖安装完成")

def main():
    manager = ServiceManager()
    
    # 注册信号处理
    def signal_handler(sig, frame):
        manager.stop_all()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == "install":
            manager.install_dependencies()
        elif command == "dev":
            if manager.start_development():
                # 保持运行
                try:
                    while True:
                        time.sleep(1)
                except KeyboardInterrupt:
                    pass
        elif command == "prod":
            manager.start_production()
        elif command == "stop":
            subprocess.run(["docker-compose", "down"], cwd=manager.base_dir)
        else:
            print("❌ 未知命令")
            print_usage()
    else:
        print_usage()

def print_usage():
    print("""
🎯 直播自动化处理系统启动脚本

用法:
  python start.py install    # 安装所有依赖
  python start.py dev        # 启动开发环境
  python start.py prod       # 启动生产环境 (Docker)
  python start.py stop       # 停止生产环境

开发环境要求:
  - Python 3.8+
  - Node.js 16+
  - FFmpeg

生产环境要求:
  - Docker
  - Docker Compose
""")

if __name__ == "__main__":
    main()
