# 前端界面 (Frontend) - 开发任务文档

## 📋 模块概述

### 职责范围
前端界面是用户与系统交互的主要入口，负责：
- 📊 **任务监控**：实时显示任务状态和进度
- ✅ **视频确认**：预览候选片段和标题选择
- 🔊 **声音提示**：新任务到达时播放提示音
- ⚙️ **系统配置**：参数设置和系统管理
- 📱 **响应式设计**：支持桌面和手机端

### 技术栈
- **框架**：Vue 3 + Composition API
- **UI组件**：Element Plus
- **构建工具**：Vite
- **状态管理**：Pinia
- **实时通信**：WebSocket
- **样式**：SCSS

## 🏗️ 目录结构

```
frontend/
├── public/
│   ├── index.html
│   ├── favicon.ico
│   └── sounds/
│       └── notification.mp3     # 通知提示音
├── src/
│   ├── main.js                  # 应用入口
│   ├── App.vue                  # 根组件
│   ├── router/
│   │   ├── index.js             # 路由配置
│   │   └── routes.js            # 路由定义
│   ├── stores/
│   │   ├── index.js             # Pinia配置
│   │   ├── task.js              # 任务状态管理
│   │   ├── system.js            # 系统状态管理
│   │   └── user.js              # 用户设置管理
│   ├── views/
│   │   ├── Dashboard.vue        # 主监控页面
│   │   ├── VideoConfirm.vue     # 视频确认页面
│   │   ├── SystemConfig.vue     # 系统配置页面
│   │   └── Mobile/
│   │       ├── MobileDashboard.vue  # 手机版监控
│   │       └── MobileConfirm.vue    # 手机版确认
│   ├── components/
│   │   ├── common/
│   │   │   ├── Header.vue       # 页面头部
│   │   │   ├── Sidebar.vue      # 侧边栏
│   │   │   └── Loading.vue      # 加载组件
│   │   ├── dashboard/
│   │   │   ├── LivePreview.vue  # 直播预览
│   │   │   ├── TaskStatus.vue   # 任务状态
│   │   │   ├── SystemStatus.vue # 系统状态
│   │   │   └── QuickActions.vue # 快速操作
│   │   ├── video/
│   │   │   ├── VideoPlayer.vue  # 视频播放器
│   │   │   ├── SegmentSelector.vue # 片段选择器
│   │   │   ├── TitleEditor.vue  # 标题编辑器
│   │   │   └── TemplateSettings.vue # 模板设置
│   │   └── notification/
│   │       ├── NotificationCenter.vue # 通知中心
│   │       └── SoundPlayer.vue  # 声音播放器
│   ├── composables/
│   │   ├── useWebSocket.js      # WebSocket组合式函数
│   │   ├── useNotification.js   # 通知组合式函数
│   │   ├── useTask.js           # 任务管理组合式函数
│   │   └── useVideo.js          # 视频处理组合式函数
│   ├── utils/
│   │   ├── api.js               # API请求封装
│   │   ├── websocket.js         # WebSocket客户端
│   │   ├── audio.js             # 音频工具
│   │   └── helpers.js           # 辅助函数
│   ├── styles/
│   │   ├── main.scss            # 主样式文件
│   │   ├── variables.scss       # SCSS变量
│   │   ├── mixins.scss          # SCSS混入
│   │   └── responsive.scss      # 响应式样式
│   └── assets/
│       ├── images/              # 图片资源
│       └── icons/               # 图标资源
├── tests/
│   ├── unit/                    # 单元测试
│   └── e2e/                     # 端到端测试
├── package.json                 # 项目配置
├── vite.config.js              # Vite配置
├── tailwind.config.js          # Tailwind配置
└── README.md                   # 本文档
```

## 📝 开发任务列表

### 1. 项目搭建（预计1天）

#### 1.1 基础环境搭建
- [ ] ⏳ 创建Vue3项目
- [ ] ⏳ 安装Element Plus
- [ ] ⏳ 配置Vite构建工具
- [ ] ⏳ 安装Pinia状态管理
- [ ] ⏳ 配置SCSS预处理器

#### 1.2 项目结构创建
- [ ] ⏳ 创建目录结构
- [ ] ⏳ 配置路由系统
- [ ] ⏳ 设置状态管理
- [ ] ⏳ 配置API请求封装

### 2. 基础组件开发（预计2天）

#### 2.1 通用组件
- [ ] ⏳ Header组件开发
- [ ] ⏳ Sidebar组件开发
- [ ] ⏳ Loading组件开发
- [ ] ⏳ 响应式布局组件

#### 2.2 WebSocket集成
- [ ] ⏳ WebSocket客户端封装
- [ ] ⏳ 实时消息处理
- [ ] ⏳ 连接状态管理
- [ ] ⏳ 重连机制实现

#### 2.3 通知系统
- [ ] ⏳ 通知中心组件
- [ ] ⏳ 声音播放器组件
- [ ] ⏳ 通知权限处理
- [ ] ⏳ 通知历史记录

### 3. 主监控页面（预计3天）

#### 3.1 页面布局设计
- [ ] ⏳ 四象限布局实现
- [ ] ⏳ 响应式设计适配
- [ ] ⏳ 组件间通信设计
- [ ] ⏳ 页面状态管理

#### 3.2 直播预览组件
- [ ] ⏳ iframe嵌入快手直播间
- [ ] ⏳ 直播状态检测
- [ ] ⏳ 预览窗口控制
- [ ] ⏳ 全屏模式支持

#### 3.3 任务状态组件
- [ ] ⏳ 任务列表展示
- [ ] ⏳ 实时状态更新
- [ ] ⏳ 进度条显示
- [ ] ⏳ 任务操作按钮

#### 3.4 系统状态组件
- [ ] ⏳ 系统健康状态
- [ ] ⏳ 服务状态监控
- [ ] ⏳ 性能指标展示
- [ ] ⏳ 图表数据可视化

#### 3.5 快速操作组件
- [ ] ⏳ 常用操作按钮
- [ ] ⏳ 批量操作功能
- [ ] ⏳ 快捷键支持
- [ ] ⏳ 操作确认对话框

### 4. 视频确认页面（预计4天）

#### 4.1 页面布局设计
- [ ] ⏳ 三栏布局实现
- [ ] ⏳ 视频播放区域
- [ ] ⏳ 分析结果展示区
- [ ] ⏳ 用户调整区域

#### 4.2 视频播放器组件
- [ ] ⏳ 自定义视频播放器
- [ ] ⏳ 片段高亮显示
- [ ] ⏳ 时间轴标记
- [ ] ⏳ 快速跳转功能
- [ ] ⏳ 倍速播放控制

#### 4.3 AI建议展示组件
- [ ] ⏳ 推荐片段展示
- [ ] ⏳ 推荐标题展示
- [ ] ⏳ 内容类型显示
- [ ] ⏳ 置信度指示器

#### 4.4 片段选择器组件
- [ ] ⏳ 候选片段列表
- [ ] ⏳ 片段预览功能
- [ ] ⏳ 选择状态管理
- [ ] ⏳ 片段对比功能

#### 4.5 标题编辑器组件
- [ ] ⏳ 标题输入框
- [ ] ⏳ 候选标题选择
- [ ] ⏳ 字数限制提示
- [ ] ⏳ 实时预览功能

#### 4.6 模板设置组件
- [ ] ⏳ 播放速度调节
- [ ] ⏳ 字幕开关控制
- [ ] ⏳ 水印设置
- [ ] ⏳ 其他剪辑参数

#### 4.7 操作按钮区域
- [ ] ⏳ 预览效果按钮
- [ ] ⏳ 确认发布按钮
- [ ] ⏳ 保存草稿按钮
- [ ] ⏳ 跳过处理按钮

### 5. 手机端适配（预计2天）

#### 5.1 响应式设计
- [ ] ⏳ 移动端布局适配
- [ ] ⏳ 触摸操作优化
- [ ] ⏳ 屏幕尺寸适配
- [ ] ⏳ 横竖屏切换

#### 5.2 手机版监控页面
- [ ] ⏳ 简化版任务列表
- [ ] ⏳ 快速状态查看
- [ ] ⏳ 移动端通知
- [ ] ⏳ 手势操作支持

#### 5.3 手机版确认页面
- [ ] ⏳ 垂直布局设计
- [ ] ⏳ 滑动切换片段
- [ ] ⏳ 简化操作界面
- [ ] ⏳ 一键确认功能

### 6. 系统配置页面（预计1天）

#### 6.1 配置表单设计
- [ ] ⏳ 分类配置界面
- [ ] ⏳ 表单验证规则
- [ ] ⏳ 配置项说明
- [ ] ⏳ 重置默认功能

#### 6.2 AI设置模块
- [ ] ⏳ API密钥配置
- [ ] ⏳ 模型选择设置
- [ ] ⏳ 功能开关控制
- [ ] ⏳ 成本监控显示

#### 6.3 剪辑模板设置
- [ ] ⏳ 默认参数配置
- [ ] ⏳ 模板预设管理
- [ ] ⏳ 自定义模板创建
- [ ] ⏳ 模板导入导出

#### 6.4 通知设置
- [ ] ⏳ 声音开关控制
- [ ] ⏳ 通知方式选择
- [ ] ⏳ 提醒时间设置
- [ ] ⏳ 免打扰模式

### 7. 性能优化和测试（预计1天）

#### 7.1 性能优化
- [ ] ⏳ 组件懒加载
- [ ] ⏳ 图片资源优化
- [ ] ⏳ 代码分割优化
- [ ] ⏳ 缓存策略实现

#### 7.2 用户体验优化
- [ ] ⏳ 加载状态优化
- [ ] ⏳ 错误提示优化
- [ ] ⏳ 操作反馈优化
- [ ] ⏳ 无障碍访问支持

#### 7.3 测试
- [ ] ⏳ 组件单元测试
- [ ] ⏳ 页面集成测试
- [ ] ⏳ 用户交互测试
- [ ] ⏳ 兼容性测试

## 🎨 设计规范

### 1. 视觉设计
- **主色调**：#409EFF (Element Plus主色)
- **辅助色**：#67C23A (成功), #E6A23C (警告), #F56C6C (错误)
- **字体**：系统默认字体栈
- **圆角**：4px (小), 8px (中), 12px (大)

### 2. 布局规范
- **栅格系统**：24栅格布局
- **间距**：8px的倍数 (8px, 16px, 24px, 32px)
- **断点**：xs(<768px), sm(≥768px), md(≥992px), lg(≥1200px)

### 3. 交互规范
- **按钮状态**：默认、悬停、激活、禁用
- **动画时长**：0.3s (快速), 0.5s (标准), 0.8s (慢速)
- **缓动函数**：ease-in-out

## 🔧 技术实现要点

### 1. WebSocket集成
```javascript
// WebSocket组合式函数
export function useWebSocket() {
  const socket = ref(null)
  const isConnected = ref(false)
  
  const connect = () => {
    socket.value = new WebSocket('ws://localhost:8080/ws')
    
    socket.value.onopen = () => {
      isConnected.value = true
    }
    
    socket.value.onmessage = (event) => {
      const data = JSON.parse(event.data)
      handleMessage(data)
    }
  }
  
  return { socket, isConnected, connect }
}
```

### 2. 视频播放器组件
```vue
<template>
  <div class="video-player">
    <video 
      ref="videoRef"
      :src="videoUrl"
      @timeupdate="onTimeUpdate"
      @loadedmetadata="onLoadedMetadata"
    />
    <div class="timeline">
      <div 
        v-for="segment in segments"
        :key="segment.id"
        class="segment-marker"
        :style="getSegmentStyle(segment)"
        @click="jumpToSegment(segment)"
      />
    </div>
  </div>
</template>
```

### 3. 状态管理
```javascript
// 任务状态管理
export const useTaskStore = defineStore('task', () => {
  const tasks = ref([])
  const currentTask = ref(null)
  
  const updateTaskStatus = (taskId, status) => {
    const task = tasks.value.find(t => t.id === taskId)
    if (task) {
      task.status = status
    }
  }
  
  return { tasks, currentTask, updateTaskStatus }
})
```

## 📊 性能要求

### 1. 加载性能
- 首屏加载时间 < 2秒
- 路由切换时间 < 500ms
- 组件渲染时间 < 100ms

### 2. 运行性能
- 内存使用 < 100MB
- CPU使用率 < 10%
- 动画帧率 > 60fps

### 3. 网络性能
- 资源压缩率 > 70%
- 图片优化率 > 50%
- 缓存命中率 > 80%

## 🧪 测试策略

### 1. 单元测试
- 组件功能测试
- 工具函数测试
- 状态管理测试

### 2. 集成测试
- 页面交互测试
- API集成测试
- WebSocket通信测试

### 3. 端到端测试
- 用户操作流程测试
- 跨浏览器兼容性测试
- 响应式设计测试

## 📝 开发注意事项

### 1. 代码规范
- 使用ESLint和Prettier
- 遵循Vue3最佳实践
- 编写清晰的组件文档
- 使用TypeScript类型检查

### 2. 用户体验
- 提供清晰的操作反馈
- 实现优雅的错误处理
- 支持键盘快捷键
- 考虑无障碍访问

### 3. 性能优化
- 使用虚拟滚动处理大列表
- 实现图片懒加载
- 优化组件重渲染
- 合理使用缓存策略

---

**模块负责人**：前端开发者  
**预计开发周期**：2-3周  
**依赖模块**：调度层、视频分析服务  
**技术栈**：Vue3 + Element Plus + Vite
