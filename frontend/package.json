{"name": "live-automation-frontend", "version": "1.0.0", "description": "直播自动化处理系统前端界面", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"vue": "^3.3.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.6.0", "echarts": "^5.4.0", "vue-echarts": "^6.6.0"}, "devDependencies": {"@vitejs/plugin-vue": "^4.4.0", "vite": "^4.5.0", "sass": "^1.69.0", "eslint": "^8.53.0", "eslint-plugin-vue": "^9.18.0", "prettier": "^3.1.0"}}