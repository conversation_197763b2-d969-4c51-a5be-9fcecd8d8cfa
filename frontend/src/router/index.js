// Vue Router 配置
// TODO: 完善路由配置

import { createRouter, createWebHistory } from 'vue-router'

const routes = [
  {
    path: '/',
    name: 'Dashboard',
    component: () => import('@/views/Dashboard.vue')
  },
  {
    path: '/confirm/:taskId',
    name: 'VideoConfirm',
    component: () => import('@/views/VideoConfirm.vue')
  },
  {
    path: '/config',
    name: 'SystemConfig',
    component: () => import('@/views/SystemConfig.vue')
  },
  {
    path: '/mobile',
    name: 'Mobile',
    component: () => import('@/views/Mobile/MobileDashboard.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router
