"""
调度层通知模块
负责在录制完成后通知调度层进行后续处理
"""
import os
import time
import requests
import configparser
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional
from src.utils import logger


class SchedulerNotifier:
    """调度层通知器"""
    
    def __init__(self, config_file: str):
        self.config_file = config_file
        self.config = configparser.RawConfigParser()
        self._load_config()
    
    def _load_config(self):
        """加载配置"""
        try:
            self.config.read(self.config_file, encoding='utf-8-sig')
            
            # 确保调度配置节存在
            if '调度配置' not in self.config.sections():
                self.config.add_section('调度配置')
                self._set_default_config()
                self._save_config()
            
            # 读取配置值
            self.enabled = self._get_bool_config('启用自动化处理', False)
            self.scheduler_url = self._get_config('调度层地址', 'http://localhost:8081')
            self.timeout = int(self._get_config('API超时时间', '10'))
            self.max_retries = int(self._get_config('重试次数', '3'))
            self.retry_interval = int(self._get_config('重试间隔', '5'))
            self.verbose_logging = self._get_bool_config('启用详细日志', False)
            
            if self.verbose_logging:
                logger.debug(f"调度层配置加载完成: 启用={self.enabled}, 地址={self.scheduler_url}")
                
        except Exception as e:
            logger.error(f"加载调度层配置失败: {e}")
            self.enabled = False
    
    def _get_config(self, option: str, default: str) -> str:
        """获取配置值"""
        try:
            return self.config.get('调度配置', option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            self.config.set('调度配置', option, default)
            self._save_config()
            return default
    
    def _get_bool_config(self, option: str, default: bool) -> bool:
        """获取布尔配置值"""
        value = self._get_config(option, "是" if default else "否")
        return value.strip() == "是"
    
    def _set_default_config(self):
        """设置默认配置"""
        defaults = {
            '启用自动化处理': '否',
            '调度层地址': 'http://localhost:8081',
            'API超时时间': '10',
            '重试次数': '3',
            '重试间隔': '5',
            '启用详细日志': '否'
        }
        
        for option, value in defaults.items():
            self.config.set('调度配置', option, value)
    
    def _save_config(self):
        """保存配置"""
        try:
            with open(self.config_file, 'w', encoding='utf-8-sig') as f:
                self.config.write(f)
        except Exception as e:
            logger.error(f"保存调度层配置失败: {e}")
    
    def get_video_duration(self, video_path: str) -> Optional[float]:
        """获取视频时长（秒）"""
        try:
            # 尝试使用ffprobe获取视频信息
            import subprocess
            import json
            
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', video_path
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                data = json.loads(result.stdout)
                duration = float(data.get('format', {}).get('duration', 0))
                return duration if duration > 0 else None
        except Exception as e:
            if self.verbose_logging:
                logger.debug(f"获取视频时长失败: {e}")
        
        # 备用方案：通过文件修改时间估算
        try:
            stat = os.stat(video_path)
            return time.time() - stat.st_mtime
        except:
            return None
    
    def get_file_size(self, file_path: str) -> Optional[int]:
        """获取文件大小（字节）"""
        try:
            return os.path.getsize(file_path)
        except Exception as e:
            if self.verbose_logging:
                logger.debug(f"获取文件大小失败: {e}")
            return None
    
    def extract_platform_from_url(self, url: str) -> str:
        """从URL提取平台名称"""
        if not url:
            return "unknown"
        
        platform_mapping = {
            'douyin.com': 'douyin',
            'tiktok.com': 'tiktok',
            'kuaishou.com': 'kuaishou',
            'huya.com': 'huya',
            'douyu.com': 'douyu',
            'yy.com': 'yy',
            'bilibili.com': 'bilibili',
            'xiaohongshu.com': 'xiaohongshu',
            'bigo.tv': 'bigo'
        }
        
        for domain, platform in platform_mapping.items():
            if domain in url:
                return platform
        
        return "unknown"
    
    def extract_room_id_from_url(self, url: str) -> str:
        """从URL提取房间ID"""
        if not url:
            return ""
        
        try:
            # 简单的房间ID提取逻辑
            parts = url.rstrip('/').split('/')
            if parts:
                return parts[-1]
        except:
            pass
        
        return ""
    
    def build_notification_data(
        self,
        record_name: str,
        save_file_path: str,
        platform: str = "unknown",
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """构建通知数据"""
        # 转换为绝对路径
        abs_path = os.path.abspath(save_file_path)

        # 获取视频信息
        duration = self.get_video_duration(abs_path)
        file_size = self.get_file_size(abs_path)

        # 构建基础数据
        data = {
            "video_path": abs_path,
            "streamer": record_name,
            "platform": platform,
            "timestamp": datetime.now().isoformat(),
        }
        
        # 添加可选字段
        if duration is not None:
            data["duration"] = duration
        
        if file_size is not None:
            data["file_size"] = file_size
        
        # 合并元数据
        if metadata:
            data["metadata"] = metadata
        else:
            data["metadata"] = {}
        
        return data
    
    def send_notification(
        self,
        record_name: str,
        save_file_path: str,
        platform: str = "unknown",
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """发送录制完成通知到调度层"""
        
        # 检查是否启用
        if not self.enabled:
            if self.verbose_logging:
                logger.debug("调度层通知未启用，跳过通知")
            return True
        
        # 检查文件是否存在
        if not os.path.exists(save_file_path):
            logger.warning(f"录制文件不存在，跳过通知: {save_file_path}")
            return False
        
        # 构建通知数据
        data = self.build_notification_data(record_name, save_file_path, platform, metadata)
        
        # 发送通知（带重试）
        for attempt in range(1, self.max_retries + 1):
            try:
                if self.verbose_logging:
                    logger.debug(f"发送调度层通知 (尝试 {attempt}/{self.max_retries}): {save_file_path}")
                
                # 发送HTTP请求
                response = requests.post(
                    f"{self.scheduler_url}/api/v1/recording/completed",
                    json=data,
                    timeout=self.timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    logger.info(f"成功通知调度层处理: {record_name} -> {save_file_path}")
                    return True
                else:
                    error_msg = f"调度层返回错误状态码: {response.status_code}"
                    if self.verbose_logging:
                        logger.warning(f"{error_msg}, 响应: {response.text}")
                    
                    if attempt < self.max_retries:
                        logger.warning(f"通知调度层失败，正在重试 ({attempt}/{self.max_retries}): {error_msg}")
                        time.sleep(self.retry_interval)
                    else:
                        logger.error(f"通知调度层最终失败: {error_msg}")
                        return False
                        
            except requests.exceptions.Timeout:
                error_msg = f"请求超时 ({self.timeout}秒)"
                if attempt < self.max_retries:
                    logger.warning(f"通知调度层失败，正在重试 ({attempt}/{self.max_retries}): {error_msg}")
                    time.sleep(self.retry_interval)
                else:
                    logger.error(f"通知调度层最终失败: {error_msg}")
                    return False
                    
            except requests.exceptions.ConnectionError:
                error_msg = "连接调度层失败，请检查调度层服务是否运行"
                if attempt < self.max_retries:
                    logger.warning(f"通知调度层失败，正在重试 ({attempt}/{self.max_retries}): {error_msg}")
                    time.sleep(self.retry_interval)
                else:
                    logger.error(f"通知调度层最终失败: {error_msg}")
                    return False
                    
            except Exception as e:
                error_msg = f"未知错误: {e}"
                if attempt < self.max_retries:
                    logger.warning(f"通知调度层失败，正在重试 ({attempt}/{self.max_retries}): {error_msg}")
                    time.sleep(self.retry_interval)
                else:
                    logger.error(f"通知调度层最终失败: {error_msg}")
                    return False
        
        return False


# 全局通知器实例（延迟初始化）
_notifier_instance = None


def get_notifier(config_file: str) -> SchedulerNotifier:
    """获取通知器实例（单例模式）"""
    global _notifier_instance
    if _notifier_instance is None or _notifier_instance.config_file != config_file:
        _notifier_instance = SchedulerNotifier(config_file)
    return _notifier_instance


def notify_scheduler_completion(
    record_name: str,
    save_file_path: str,
    platform: str = "unknown",
    metadata: Optional[Dict[str, Any]] = None,
    config_file: str = "config/config.ini"
) -> bool:
    """
    通知调度层录制完成
    
    Args:
        record_name: 录制名称/主播名
        save_file_path: 保存文件路径
        platform: 平台名称
        metadata: 额外元数据
        config_file: 配置文件路径
    
    Returns:
        bool: 通知是否成功
    """
    try:
        notifier = get_notifier(config_file)
        return notifier.send_notification(record_name, save_file_path, platform, metadata)
    except Exception as e:
        logger.error(f"调度层通知异常: {e}")
        return False
