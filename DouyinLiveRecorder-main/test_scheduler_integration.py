"""
录制库调度层集成测试脚本
"""
import os
import sys
import time
import tempfile
import configparser
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.scheduler_notifier import SchedulerNotifier, notify_scheduler_completion
import src.scheduler_notifier as notifier_module


def create_test_config(config_path: str, enabled: bool = True, scheduler_url: str = "http://localhost:8081"):
    """创建测试配置文件"""
    config = configparser.RawConfigParser()
    
    # 添加基础配置节
    config.add_section('录制设置')
    config.set('录制设置', '直播保存路径(不填则默认)', '')
    
    # 添加调度配置节
    config.add_section('调度配置')
    config.set('调度配置', '启用自动化处理', '是' if enabled else '否')
    config.set('调度配置', '调度层地址', scheduler_url)
    config.set('调度配置', 'API超时时间', '10')
    config.set('调度配置', '重试次数', '3')
    config.set('调度配置', '重试间隔', '5')
    config.set('调度配置', '启用详细日志', '是')
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8-sig') as f:
        config.write(f)
    
    print(f"✅ 测试配置文件已创建: {config_path}")


def create_test_video(video_path: str):
    """创建测试视频文件"""
    # 创建一个假的视频文件
    with open(video_path, 'wb') as f:
        f.write(b'fake video content for testing')
    
    print(f"✅ 测试视频文件已创建: {video_path}")


def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试1: 配置加载")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')
        
        # 测试启用状态
        create_test_config(config_path, enabled=True)
        notifier = SchedulerNotifier(config_path)
        
        assert notifier.enabled == True, "配置启用状态错误"
        assert notifier.scheduler_url == "http://localhost:8081", "调度层地址错误"
        assert notifier.timeout == 10, "超时时间错误"
        assert notifier.max_retries == 3, "重试次数错误"
        
        print("✅ 配置加载测试通过")
        
        # 测试禁用状态
        create_test_config(config_path, enabled=False)
        notifier = SchedulerNotifier(config_path)
        
        assert notifier.enabled == False, "配置禁用状态错误"
        
        print("✅ 配置禁用测试通过")


def test_video_info_extraction():
    """测试视频信息提取"""
    print("\n🔍 测试2: 视频信息提取")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')
        video_path = os.path.join(temp_dir, 'test_video.mp4')
        
        create_test_config(config_path)
        create_test_video(video_path)
        
        notifier = SchedulerNotifier(config_path)
        
        # 测试文件大小获取
        file_size = notifier.get_file_size(video_path)
        assert file_size is not None and file_size > 0, "文件大小获取失败"
        print(f"✅ 文件大小获取成功: {file_size} 字节")
        
        # 测试平台提取
        test_urls = [
            ("https://live.douyin.com/123456", "douyin"),
            ("https://www.kuaishou.com/live/123", "kuaishou"),
            ("https://www.huya.com/123456", "huya"),
            ("https://unknown.com/123", "unknown")
        ]
        
        for url, expected_platform in test_urls:
            platform = notifier.extract_platform_from_url(url)
            assert platform == expected_platform, f"平台提取错误: {url} -> {platform} (期望: {expected_platform})"
        
        print("✅ 平台提取测试通过")
        
        # 测试房间ID提取
        room_id = notifier.extract_room_id_from_url("https://live.douyin.com/123456")
        assert room_id == "123456", f"房间ID提取错误: {room_id}"
        
        print("✅ 房间ID提取测试通过")


def test_notification_data_building():
    """测试通知数据构建"""
    print("\n🔍 测试3: 通知数据构建")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')
        video_path = os.path.join(temp_dir, 'test_video.mp4')
        
        create_test_config(config_path)
        create_test_video(video_path)
        
        notifier = SchedulerNotifier(config_path)
        
        # 构建通知数据
        metadata = {
            "quality": "原画",
            "format": "mp4",
            "room_id": "123456"
        }
        
        data = notifier.build_notification_data(
            record_name="测试主播",
            save_file_path=video_path,
            platform="douyin",
            metadata=metadata
        )
        
        # 验证数据结构
        required_fields = ["video_path", "streamer", "platform", "timestamp", "file_size", "metadata"]
        for field in required_fields:
            assert field in data, f"缺少必要字段: {field}"
        
        assert data["streamer"] == "测试主播", "主播名称错误"
        assert data["platform"] == "douyin", "平台名称错误"
        assert data["video_path"] == video_path, "视频路径错误"
        assert data["file_size"] > 0, "文件大小错误"
        assert data["metadata"]["quality"] == "原画", "元数据错误"
        
        print("✅ 通知数据构建测试通过")
        print(f"   数据示例: {data}")


def test_disabled_notification():
    """测试禁用状态下的通知"""
    print("\n🔍 测试4: 禁用状态通知")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')
        video_path = os.path.join(temp_dir, 'test_video.mp4')
        
        # 创建禁用配置
        create_test_config(config_path, enabled=False)
        create_test_video(video_path)
        
        # 测试通知函数
        result = notify_scheduler_completion(
            record_name="测试主播",
            save_file_path=video_path,
            platform="douyin",
            config_file=config_path
        )
        
        assert result == True, "禁用状态下应该返回True"
        print("✅ 禁用状态通知测试通过")


def test_notification_with_scheduler_running():
    """测试与运行中的调度层通信"""
    print("\n🔍 测试5: 调度层通信测试")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')
        video_path = os.path.join(temp_dir, 'test_video.mp4')
        
        create_test_config(config_path, enabled=True)
        create_test_video(video_path)
        
        # 测试通知函数
        result = notify_scheduler_completion(
            record_name="测试主播",
            save_file_path=video_path,
            platform="douyin",
            metadata={
                "quality": "原画",
                "format": "mp4",
                "room_id": "123456"
            },
            config_file=config_path
        )
        
        if result:
            print("✅ 调度层通信成功")
        else:
            print("⚠️  调度层通信失败（可能调度层未运行）")
        
        return result


def test_error_handling():
    """测试错误处理"""
    print("\n🔍 测试6: 错误处理")

    # 重置单例实例
    notifier_module._notifier_instance = None

    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = os.path.join(temp_dir, 'test_config.ini')

        # 测试不存在的文件（启用状态）
        create_test_config(config_path, enabled=True, scheduler_url="http://localhost:8081")

        result = notify_scheduler_completion(
            record_name="测试主播",
            save_file_path="/path/to/nonexistent/file.mp4",
            platform="douyin",
            config_file=config_path
        )

        assert result == False, "不存在的文件应该返回False"
        print("✅ 文件不存在错误处理测试通过")

        # 测试无效的调度层地址
        notifier_module._notifier_instance = None  # 重置实例
        create_test_config(config_path, enabled=True, scheduler_url="http://invalid-url:9999")
        video_path = os.path.join(temp_dir, 'test_video.mp4')
        create_test_video(video_path)

        result = notify_scheduler_completion(
            record_name="测试主播",
            save_file_path=video_path,
            platform="douyin",
            config_file=config_path
        )

        assert result == False, "无效地址应该返回False"
        print("✅ 无效地址错误处理测试通过")


def main():
    """主测试函数"""
    print("🚀 录制库调度层集成测试开始")
    print("=" * 50)
    
    try:
        # 基础功能测试
        test_config_loading()
        test_video_info_extraction()
        test_notification_data_building()
        test_disabled_notification()
        test_error_handling()
        
        # 实际通信测试
        scheduler_running = test_notification_with_scheduler_running()
        
        print("\n📊 测试结果统计:")
        print("✅ 配置加载: 通过")
        print("✅ 视频信息提取: 通过")
        print("✅ 通知数据构建: 通过")
        print("✅ 禁用状态处理: 通过")
        print("✅ 错误处理: 通过")
        print(f"{'✅' if scheduler_running else '⚠️ '} 调度层通信: {'通过' if scheduler_running else '失败（调度层可能未运行）'}")
        
        print("\n🎉 录制库改造集成测试完成！")
        
        if not scheduler_running:
            print("\n💡 提示: 要测试完整功能，请先启动调度层服务:")
            print("   cd scheduler && python run_server.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
