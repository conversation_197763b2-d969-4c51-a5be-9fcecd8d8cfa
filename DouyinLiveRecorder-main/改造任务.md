# 录制库改造 - 开发任务文档

## 📋 模块概述

### 改造目标
对现有的DouyinLiveRecorder录制库进行最小化改造，添加录制完成后的API通知功能，使其能够与调度层进行通信。

### 改造原则
- ✅ **最小改动**：保持现有功能不变，只添加必要功能
- ✅ **向后兼容**：改造后仍可独立运行
- ✅ **可配置**：通过配置文件控制是否启用自动化处理
- ✅ **容错性**：API调用失败不影响录制功能

### 技术要求
- **HTTP客户端**：requests库
- **配置管理**：现有的configparser
- **日志记录**：现有的logging系统
- **错误处理**：异常捕获和重试机制

## 🔍 现有代码分析

### 关键文件分析
- **main.py**：主程序文件，包含录制逻辑
- **config/config.ini**：配置文件
- **src/**：核心功能模块

### 录制完成触发点
根据代码分析，录制完成的关键位置在：
- **文件位置**：`main.py` 第427行附近
- **触发条件**：录制停止并保存文件后
- **可用变量**：record_name, save_file_path, stop_time等

## 📝 改造任务列表

### 1. 代码分析和准备（预计0.5天）

#### 1.1 深入分析现有代码
- [ ] ⏳ 详细阅读main.py录制逻辑
- [ ] ⏳ 分析录制完成的准确触发点
- [ ] ⏳ 确认可用的变量和数据
- [ ] ⏳ 分析现有的错误处理机制

#### 1.2 设计改造方案
- [ ] ⏳ 设计API通知数据结构
- [ ] ⏳ 设计配置项结构
- [ ] ⏳ 设计错误处理策略
- [ ] ⏳ 制定测试方案

### 2. 配置文件更新（预计0.5天）

#### 2.1 添加调度层配置
- [ ] ⏳ 在config.ini中添加[调度配置]节
- [ ] ⏳ 添加调度层地址配置项
- [ ] ⏳ 添加功能开关配置项
- [ ] ⏳ 添加重试次数配置项

#### 2.2 配置项说明
```ini
[调度配置]
# 是否启用自动化处理
启用自动化处理 = 是

# 调度层API地址
调度层地址 = http://localhost:8080

# API调用超时时间（秒）
API超时时间 = 10

# 失败重试次数
重试次数 = 3

# 重试间隔（秒）
重试间隔 = 5
```

### 3. 核心功能开发（预计1天）

#### 3.1 API通知功能开发
- [ ] ⏳ 实现通知函数
- [ ] ⏳ 构建通知数据结构
- [ ] ⏳ 实现HTTP POST请求
- [ ] ⏳ 添加请求超时处理

#### 3.2 错误处理机制
- [ ] ⏳ 实现异常捕获
- [ ] ⏳ 实现重试机制
- [ ] ⏳ 添加日志记录
- [ ] ⏳ 确保录制功能不受影响

#### 3.3 配置读取功能
- [ ] ⏳ 读取调度层配置
- [ ] ⏳ 验证配置有效性
- [ ] ⏳ 处理配置缺失情况
- [ ] ⏳ 支持配置热更新

### 4. 代码集成（预计0.5天）

#### 4.1 在录制完成处添加调用
- [ ] ⏳ 定位准确的插入位置
- [ ] ⏳ 添加通知函数调用
- [ ] ⏳ 传递必要的参数
- [ ] ⏳ 确保不影响原有逻辑

#### 4.2 依赖包管理
- [ ] ⏳ 添加requests依赖
- [ ] ⏳ 更新requirements.txt
- [ ] ⏳ 检查依赖冲突
- [ ] ⏳ 测试依赖安装

### 5. 测试和验证（预计0.5天）

#### 5.1 功能测试
- [ ] ⏳ 测试录制功能正常
- [ ] ⏳ 测试API通知功能
- [ ] ⏳ 测试配置开关功能
- [ ] ⏳ 测试错误处理机制

#### 5.2 集成测试
- [ ] ⏳ 与调度层联调测试
- [ ] ⏳ 测试网络异常情况
- [ ] ⏳ 测试配置错误情况
- [ ] ⏳ 验证日志记录

## 🔧 技术实现方案

### 1. 通知函数实现
```python
def notify_scheduler(record_name, save_file_path, metadata):
    """
    通知调度层录制完成
    
    Args:
        record_name: 录制名称（主播名称）
        save_file_path: 保存文件路径
        metadata: 录制元数据
    """
    try:
        # 读取配置
        config = configparser.ConfigParser()
        config.read('./config/config.ini', encoding='utf-8')
        
        # 检查是否启用自动化处理
        if not config.getboolean('调度配置', '启用自动化处理', fallback=False):
            logger.info("自动化处理未启用，跳过通知")
            return
        
        # 获取配置参数
        scheduler_url = config.get('调度配置', '调度层地址', fallback='http://localhost:8080')
        timeout = config.getint('调度配置', 'API超时时间', fallback=10)
        max_retries = config.getint('调度配置', '重试次数', fallback=3)
        retry_interval = config.getint('调度配置', '重试间隔', fallback=5)
        
        # 构建通知数据
        notification_data = {
            "video_path": save_file_path,
            "duration": get_video_duration(save_file_path),  # 获取视频时长
            "streamer": record_name,
            "platform": metadata.get("platform", "unknown"),
            "timestamp": time.strftime('%Y-%m-%d %H:%M:%S'),
            "file_size": os.path.getsize(save_file_path),
            "metadata": {
                "quality": metadata.get("quality", ""),
                "format": metadata.get("format", ""),
                "room_id": metadata.get("room_id", "")
            }
        }
        
        # 发送通知（带重试）
        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{scheduler_url}/api/v1/recording/completed",
                    json=notification_data,
                    timeout=timeout,
                    headers={'Content-Type': 'application/json'}
                )
                
                if response.status_code == 200:
                    logger.info(f"成功通知调度层处理: {save_file_path}")
                    return
                else:
                    logger.warning(f"调度层响应异常: {response.status_code}")
                    
            except requests.exceptions.RequestException as e:
                logger.warning(f"通知调度层失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                
                if attempt < max_retries - 1:
                    time.sleep(retry_interval)
                    
        logger.error(f"通知调度层最终失败，已重试 {max_retries} 次")
        
    except Exception as e:
        logger.error(f"通知调度层异常: {e}")
        # 确保异常不影响录制功能
        pass

def get_video_duration(video_path):
    """获取视频时长（秒）"""
    try:
        import cv2
        cap = cv2.VideoCapture(video_path)
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
        duration = frame_count / fps if fps > 0 else 0
        cap.release()
        return int(duration)
    except:
        return 0
```

### 2. 配置文件更新
在 `config/config.ini` 文件末尾添加：
```ini
[调度配置]
启用自动化处理 = 是
调度层地址 = http://localhost:8080
API超时时间 = 10
重试次数 = 3
重试间隔 = 5
```

### 3. 主程序集成
在 `main.py` 第427行附近添加：
```python
# 原有代码
print(f"\n{record_name} {stop_time} 直播录制完成\n")

# 新增代码：通知调度层
try:
    metadata = {
        "platform": platform,
        "quality": record_quality,
        "format": "mp4",
        "room_id": room_id if 'room_id' in locals() else ""
    }
    notify_scheduler(record_name, save_file_path, metadata)
except Exception as e:
    logger.error(f"调用通知函数异常: {e}")
```

### 4. 依赖更新
在 `requirements.txt` 中确保包含：
```
requests>=2.25.0
opencv-python>=4.5.0  # 用于获取视频时长
```

## 📊 测试方案

### 1. 单元测试
- **通知函数测试**：验证数据构建和发送逻辑
- **配置读取测试**：验证配置项解析
- **错误处理测试**：验证异常捕获和重试

### 2. 集成测试
- **录制流程测试**：确保改造后录制功能正常
- **API通信测试**：与调度层的通信测试
- **网络异常测试**：模拟网络故障情况

### 3. 兼容性测试
- **配置兼容测试**：新旧配置文件兼容性
- **功能开关测试**：启用/禁用自动化处理
- **独立运行测试**：不依赖调度层独立运行

## 📝 注意事项

### 1. 改造原则
- **最小侵入**：尽量减少对原有代码的修改
- **向后兼容**：确保改造后仍可独立使用
- **容错设计**：API调用失败不影响录制功能
- **配置驱动**：通过配置控制新功能的启用

### 2. 错误处理
- **网络异常**：超时、连接失败等
- **配置错误**：地址错误、参数缺失等
- **数据异常**：文件不存在、权限问题等
- **系统异常**：内存不足、磁盘空间等

### 3. 日志记录
- **成功通知**：记录INFO级别日志
- **重试过程**：记录WARNING级别日志
- **最终失败**：记录ERROR级别日志
- **异常情况**：记录详细的异常信息

### 4. 性能考虑
- **异步处理**：考虑使用线程避免阻塞录制
- **超时控制**：设置合理的API调用超时时间
- **资源清理**：及时释放网络连接资源
- **内存使用**：避免大数据量的内存占用

---

**模块负责人**：开发者  
**预计改造周期**：0.5周  
**风险等级**：低  
**依赖模块**：调度层（需要先启动调度层进行联调测试）
