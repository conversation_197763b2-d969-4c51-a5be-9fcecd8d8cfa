"""
端到端测试：模拟真实的录制完成场景
"""
import os
import sys
import time
import tempfile
import configparser
from pathlib import Path

# 添加src目录到路径
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.scheduler_notifier import notify_scheduler_completion


def create_realistic_config():
    """创建真实的配置文件"""
    config_path = "config/config.ini"
    
    # 读取现有配置
    config = configparser.RawConfigParser()
    config.read(config_path, encoding='utf-8-sig')
    
    # 确保调度配置存在
    if '调度配置' not in config.sections():
        config.add_section('调度配置')
    
    # 设置为启用状态
    config.set('调度配置', '启用自动化处理', '是')
    config.set('调度配置', '调度层地址', 'http://localhost:8081')
    config.set('调度配置', 'API超时时间', '10')
    config.set('调度配置', '重试次数', '3')
    config.set('调度配置', '重试间隔', '5')
    config.set('调度配置', '启用详细日志', '是')
    
    # 保存配置
    with open(config_path, 'w', encoding='utf-8-sig') as f:
        config.write(f)
    
    print(f"✅ 配置文件已更新: {config_path}")
    return config_path


def create_test_video_file():
    """创建测试视频文件"""
    # 创建录制目录
    record_dir = Path("recordings")
    record_dir.mkdir(exist_ok=True)
    
    # 创建测试视频文件
    video_path = record_dir / "测试主播_2025-01-25_14-30-00.mp4"
    
    # 写入一些测试数据
    with open(video_path, 'wb') as f:
        f.write(b'fake video content for end-to-end testing' * 1000)  # 约43KB
    
    print(f"✅ 测试视频文件已创建: {video_path} (大小: {video_path.stat().st_size} 字节)")
    return str(video_path)


def simulate_douyin_recording_completion():
    """模拟抖音录制完成场景"""
    print("\n🎬 模拟抖音录制完成场景")
    
    # 创建配置和视频文件
    config_path = create_realistic_config()
    video_path = create_test_video_file()
    
    # 模拟录制完成的元数据
    metadata = {
        "quality": "原画",
        "format": "mp4",
        "room_id": "123456789",
        "title": "测试直播间",
        "stop_time": "2025-01-25 14:30:00",
        "recording_duration": "00:30:15"
    }
    
    print(f"📹 主播: 测试主播")
    print(f"📱 平台: douyin")
    print(f"📁 文件: {video_path}")
    print(f"📊 元数据: {metadata}")
    
    # 发送通知
    print("\n📡 发送录制完成通知...")
    result = notify_scheduler_completion(
        record_name="测试主播",
        save_file_path=video_path,
        platform="douyin",
        metadata=metadata,
        config_file=config_path
    )
    
    if result:
        print("✅ 录制完成通知发送成功！")
        print("🎯 调度层应该已经接收到通知并创建了新任务")
    else:
        print("❌ 录制完成通知发送失败")
    
    return result


def simulate_kuaishou_recording_completion():
    """模拟快手录制完成场景"""
    print("\n🎬 模拟快手录制完成场景")
    
    # 创建另一个测试视频文件
    record_dir = Path("recordings")
    video_path = record_dir / "快手主播_2025-01-25_15-00-00.flv"
    
    with open(video_path, 'wb') as f:
        f.write(b'fake kuaishou video content' * 2000)  # 约54KB
    
    print(f"✅ 快手视频文件已创建: {video_path} (大小: {video_path.stat().st_size} 字节)")
    
    # 模拟快手录制完成的元数据
    metadata = {
        "quality": "超清",
        "format": "flv",
        "room_id": "ks_987654321",
        "title": "快手测试直播",
        "stop_time": "2025-01-25 15:00:00"
    }
    
    print(f"📹 主播: 快手主播")
    print(f"📱 平台: kuaishou")
    print(f"📁 文件: {video_path}")
    print(f"📊 元数据: {metadata}")
    
    # 发送通知
    print("\n📡 发送录制完成通知...")
    result = notify_scheduler_completion(
        record_name="快手主播",
        save_file_path=str(video_path),
        platform="kuaishou",
        metadata=metadata
    )
    
    if result:
        print("✅ 快手录制完成通知发送成功！")
    else:
        print("❌ 快手录制完成通知发送失败")
    
    return result


def test_disabled_scenario():
    """测试禁用场景"""
    print("\n🚫 测试禁用自动化处理场景")
    
    # 临时禁用自动化处理
    config_path = "config/config.ini"
    config = configparser.RawConfigParser()
    config.read(config_path, encoding='utf-8-sig')
    
    # 保存原始状态
    original_enabled = config.get('调度配置', '启用自动化处理')
    
    # 设置为禁用
    config.set('调度配置', '启用自动化处理', '否')
    with open(config_path, 'w', encoding='utf-8-sig') as f:
        config.write(f)
    
    print("⚙️  已临时禁用自动化处理")
    
    # 创建测试视频
    record_dir = Path("recordings")
    video_path = record_dir / "禁用测试_2025-01-25_16-00-00.mp4"
    
    with open(video_path, 'wb') as f:
        f.write(b'disabled test video content')
    
    # 尝试发送通知
    result = notify_scheduler_completion(
        record_name="禁用测试主播",
        save_file_path=str(video_path),
        platform="douyin"
    )
    
    # 恢复原始状态
    config.set('调度配置', '启用自动化处理', original_enabled)
    with open(config_path, 'w', encoding='utf-8-sig') as f:
        config.write(f)
    
    print("⚙️  已恢复原始配置")
    
    if result:
        print("✅ 禁用状态下正确跳过通知")
    else:
        print("❌ 禁用状态处理异常")
    
    return result


def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件...")
    
    test_files = [
        "recordings/测试主播_2025-01-25_14-30-00.mp4",
        "recordings/快手主播_2025-01-25_15-00-00.flv",
        "recordings/禁用测试_2025-01-25_16-00-00.mp4"
    ]
    
    for file_path in test_files:
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                print(f"🗑️  已删除: {file_path}")
        except Exception as e:
            print(f"⚠️  删除失败: {file_path} - {e}")
    
    # 如果recordings目录为空，删除它
    try:
        recordings_dir = Path("recordings")
        if recordings_dir.exists() and not any(recordings_dir.iterdir()):
            recordings_dir.rmdir()
            print("🗑️  已删除空的recordings目录")
    except:
        pass


def main():
    """主测试函数"""
    print("🚀 录制库端到端测试开始")
    print("=" * 60)
    print("📋 测试场景:")
    print("   1. 抖音录制完成通知")
    print("   2. 快手录制完成通知")
    print("   3. 禁用状态测试")
    print("=" * 60)
    
    results = []
    
    try:
        # 场景1：抖音录制完成
        results.append(simulate_douyin_recording_completion())
        
        # 等待一下
        time.sleep(2)
        
        # 场景2：快手录制完成
        results.append(simulate_kuaishou_recording_completion())
        
        # 等待一下
        time.sleep(2)
        
        # 场景3：禁用状态测试
        results.append(test_disabled_scenario())
        
        # 统计结果
        success_count = sum(results)
        total_count = len(results)
        
        print("\n" + "=" * 60)
        print("📊 端到端测试结果统计:")
        print(f"✅ 成功场景: {success_count}/{total_count}")
        print(f"📈 成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == total_count:
            print("\n🎉 所有端到端测试场景都成功！")
            print("🎯 录制库改造完成，已成功集成调度层通信功能")
            print("\n💡 使用说明:")
            print("   1. 确保调度层服务正在运行 (http://localhost:8081)")
            print("   2. 在config/config.ini中启用自动化处理")
            print("   3. 正常使用录制库，录制完成后会自动通知调度层")
        else:
            print(f"\n⚠️  部分测试场景失败 ({total_count - success_count}/{total_count})")
            print("请检查调度层服务状态和配置")
        
        return success_count == total_count
        
    except Exception as e:
        print(f"\n❌ 端到端测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理测试文件
        cleanup_test_files()


if __name__ == "__main__":
    success = main()
    print(f"\n{'🎉 测试成功' if success else '❌ 测试失败'}")
    exit(0 if success else 1)
