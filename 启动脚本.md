# 项目启动脚本

## 🚀 一键启动（推荐）

### 统一启动脚本
```bash
# 安装所有依赖
python start.py install

# 启动开发环境
python start.py dev

# 启动生产环境（Docker）
python start.py prod

# 停止生产环境
python start.py stop
```

## 开发环境手动启动

### 1. 调度层服务
```bash
cd scheduler
pip install -r requirements.txt
python app.py
# 服务地址: http://localhost:8080
```

### 2. 视频分析服务
```bash
cd video_analysis
pip install -r requirements.txt
python app.py
# 服务地址: http://localhost:8000
```

### 3. 上传服务
```bash
cd auto_upload
pip install -r requirements.txt
python api_server.py
# 服务地址: http://localhost:8001
```

### 4. 前端界面
```bash
cd frontend
npm install
npm run dev
# 服务地址: http://localhost:8888
```

### 5. 录制库（可选）
```bash
cd DouyinLiveRecorder-main
pip install -r requirements.txt
python main.py
```

## 服务端口分配

| 服务 | 端口 | 说明 |
|------|------|------|
| 调度层 | 8080 | 核心协调服务 |
| 视频分析 | 8000 | 视频处理服务 |
| 上传服务 | 8001 | 上传API服务 |
| 前端界面 | 8888 | Web用户界面 |

## 健康检查

启动所有服务后，可以通过以下地址检查服务状态：

- 调度层: http://localhost:8080/health
- 视频分析: http://localhost:8000/health  
- 上传服务: http://localhost:8001/health
- 前端界面: http://localhost:8888

## 开发工具推荐

### Python开发
- IDE: PyCharm / VSCode
- 虚拟环境: venv / conda
- 代码格式化: black, isort
- 类型检查: mypy

### 前端开发
- IDE: VSCode
- 浏览器: Chrome DevTools
- 调试工具: Vue DevTools

## 常见问题

### 1. 端口冲突
如果端口被占用，可以修改各服务的端口配置

### 2. 依赖安装失败
确保Python版本 >= 3.8，Node.js版本 >= 16

### 3. 服务无法启动
检查防火墙设置和端口权限
