# 直播自动化处理系统 - 架构设计文档

## 📋 项目概述

### 项目目标
构建一个从直播录制到自动剪辑上传的完整自动化工作流系统，主要服务于单个直播间的内容处理。

### 核心价值
- 🎯 **效率提升**：从手工处理10条视频/天 → 自动化处理
- 🎯 **质量保证**：AI分析生成标题，模板化剪辑确保一致性
- 🎯 **用户体验**：手机端快速确认，5分钟内完成人工干预

### 技术栈选择
- **后端框架**：FastAPI + SQLite
- **前端框架**：Vue 3 + Element Plus + WebSocket
- **AI服务**：DeepSeek API (可选OpenAI)
- **视频处理**：FFmpeg + OpenCV + pyJianYingDraft

## 🏗️ 系统架构

### 整体架构图
```
┌─────────────────┐    HTTP API    ┌─────────────────┐    HTTP API    ┌─────────────────┐
│   录制库        │ ──────────────→ │    调度层       │ ──────────────→ │   上传服务      │
│ DouyinLive      │                │  Scheduler      │                │ auto_upload     │
│ Recorder        │                │   (核心)        │                │                 │
└─────────────────┘                └─────────────────┘                └─────────────────┘
                                           │
                                           │ HTTP API
                                           ▼
                                   ┌─────────────────┐
                                   │  视频分析服务   │
                                   │ VideoAnalysis   │
                                   │  (当前项目)     │
                                   └─────────────────┘
                                           │
                                           │ WebSocket
                                           ▼
                                   ┌─────────────────┐
                                   │   Web界面       │
                                   │ Vue3 + Element  │
                                   └─────────────────┘
```

### 数据流向
```
录制完成 → 调度层 → 视频分析 → 用户确认 → 视频生成 → 上传服务 → 完成通知
```

## 📦 组件详细设计

### 1. 录制库 (DouyinLiveRecorder)

#### 职责范围
- ✅ **保持现有功能**：监听直播、自动录制、文件管理
- 🔧 **新增功能**：录制完成后通知调度层

#### 改动内容
**文件位置**：`main.py` 第427行附近

**功能描述**：
- 在录制完成处添加HTTP API调用
- 向调度层发送录制完成通知
- 包含视频路径、时长、主播信息等元数据

#### 配置要求
在 `config.ini` 中添加调度层配置：
- 调度层地址
- 是否启用自动化处理
- 通知重试次数

### 2. 调度层 (Scheduler) - 新开发

#### 职责范围
- 🎯 **任务管理**：创建、跟踪、更新任务状态
- 🎯 **工作流编排**：协调各服务间的调用
- 🎯 **用户交互**：处理确认请求和响应
- 🎯 **错误处理**：重试机制和异常恢复
- 🎯 **通知服务**：WebSocket实时通知

#### 技术栈
- **框架**：FastAPI
- **数据库**：SQLite
- **异步处理**：asyncio + 任务队列
- **实时通信**：WebSocket

#### 核心模块设计

##### 2.1 任务管理器
- 创建新任务，生成唯一task_id
- 更新任务状态和进度
- 查询任务详细信息
- 管理任务生命周期

##### 2.2 工作流编排器
- 启动视频分析流程
- 处理分析完成事件
- 处理用户确认选择
- 启动上传流程
- 处理上传完成事件

##### 2.3 通知服务
- WebSocket实时推送
- 任务状态变更通知
- 用户确认请求通知
- 系统状态监控

#### API接口设计
- `POST /api/v1/recording/completed` - 接收录制完成通知
- `GET /api/v1/tasks/{task_id}/status` - 查询任务状态
- `GET /api/v1/tasks/pending` - 获取待确认任务
- `POST /api/v1/tasks/{task_id}/confirm` - 用户确认
- `GET /api/v1/system/status` - 系统状态查询

#### 数据库设计
**任务表 (tasks)**：
- id, video_path, streamer, platform, status, progress
- current_step, created_at, updated_at, metadata

**处理结果表 (processing_results)**：
- task_id, segment_candidates, title_candidates
- selected_options, final_video_path

**上传记录表 (upload_records)**：
- task_id, platform, upload_status, upload_url
- upload_time, error_message

### 3. 视频分析服务 (VideoAnalysis) - 当前项目

#### 职责范围
- 🎯 **精华提取**：从10分钟视频提取2-3分钟精华片段
- 🎯 **内容分析**：AI分析视频内容
- 🎯 **标题生成**：生成3-5个候选标题
- 🎯 **剪映集成**：使用pyJianYingDraft生成草稿
- 🎯 **视频生成**：根据用户选择生成最终视频

#### 技术栈
- **框架**：FastAPI
- **AI服务**：DeepSeek API (标题生成)
- **音频处理**：Whisper (语音转文字)
- **视频处理**：OpenCV, FFmpeg
- **剪辑集成**：pyJianYingDraft

#### 核心模块设计

##### 3.1 精华提取器
**多策略提取**：
- 音频能量最高的片段
- 固定黄金时段（第2-5分钟）
- 中间部分避免开头结尾
- 返回2-3个候选片段

##### 3.2 内容分析器
**分析内容**：
- 音频转文字（Whisper）
- 提取关键词
- 场景分析
- 内容类型分类

##### 3.3 标题生成器
**多层级生成**：
- 模板化标题（免费）
- AI生成标题（付费可选）
- 关键词组合标题
- 返回3-5个候选标题

##### 3.4 剪映集成器
**自动化剪辑**：
- 创建剪映草稿
- 设置1.2倍速
- 添加字幕和水印
- 应用剪辑模板

#### API接口设计
- `POST /api/v1/analyze/start` - 开始分析
- `GET /api/v1/analyze/status/{task_id}` - 查询分析状态
- `GET /api/v1/analyze/result/{task_id}` - 获取分析结果
- `POST /api/v1/analyze/generate` - 生成最终视频

### 4. 上传服务 (auto_upload)

#### 职责范围
- ✅ **保持现有功能**：快手平台上传逻辑
- 🔧 **新增功能**：HTTP API接口包装

#### 改动内容
**新增文件**：`api_server.py`

**功能描述**：
- 将现有上传器包装成API服务
- 支持异步上传处理
- 上传完成后通知调度层
- 提供上传状态查询

#### API接口设计
- `POST /api/v1/upload/start` - 开始上传
- `GET /api/v1/upload/status/{task_id}` - 查询上传状态

## 🎨 前端需求文档

### 技术栈
- **框架**：Vue 3 + Composition API
- **UI组件**：Element Plus
- **实时通信**：WebSocket
- **构建工具**：Vite

### 页面设计需求

#### 1. 主监控页面 (桌面版)

**布局要求**：
```
┌─────────────────┬─────────────────┐
│   直播间预览    │    任务状态     │
│   (iframe)      │   (实时更新)    │
├─────────────────┼─────────────────┤
│   系统状态      │    快速操作     │
│   (图表展示)    │   (按钮组)      │
└─────────────────┴─────────────────┘
```

**功能需求**：
- 📺 **直播间预览**：嵌入快手直播间页面
- 📊 **实时任务状态**：WebSocket更新任务进度
- 🔊 **声音提示**：新任务到达播放提示音
- ⚡ **快速操作**：暂停录制、手动触发处理等
- 📈 **数据统计**：今日处理数量、成功率等

#### 2. 视频确认页面 (核心页面)

**新的确认流程设计**：

**页面布局**：
```
┌─────────────────────────────────────┐
│           视频播放区域              │
│     (支持片段切换和时间轴标记)      │
├─────────────────┬───────────────────┤
│   AI分析建议    │    用户调整区域   │
│   - 推荐片段    │    - 片段选择     │
│   - 推荐标题    │    - 标题编辑     │
│   - 内容类型    │    - 剪辑设置     │
└─────────────────┴───────────────────┘
│              操作按钮区域           │
│   预览效果 | 确认发布 | 保存草稿    │
└─────────────────────────────────────┘
```

**核心功能**：
- 🎬 **视频播放器**：支持片段高亮和快速跳转
- 🤖 **AI建议展示**：显示推荐片段、标题、内容类型
- ✏️ **用户调整**：片段选择、标题编辑、剪辑参数调整
- 👀 **实时预览**：调整后可预览效果
- ⚡ **快速操作**：确认发布、保存草稿、跳过

**用户交互流程**：
1. 用户观看完整视频
2. 查看AI分析建议
3. 根据个人判断调整选择
4. 预览剪辑效果
5. 确认发布或保存草稿

#### 3. 手机端确认页面

**简化版设计**：
- 📱 **视频预览**：可滑动切换候选片段
- 📝 **快速编辑**：标题快速修改
- ⚡ **一键操作**：确认、跳过、自定义
- 📊 **状态显示**：当前任务进度

#### 4. 系统配置页面

**配置分类**：
- 🔧 **AI设置**：API密钥、模型选择、启用开关
- 📹 **剪辑模板**：默认速度、水印设置、字幕样式
- 🔔 **通知设置**：声音开关、确认超时时间
- 📊 **统计设置**：数据保留时间、报表配置

### WebSocket 事件设计

**前端监听事件**：
- `task.created` - 新任务通知
- `task.status_updated` - 任务状态更新
- `task.analysis_completed` - 分析完成，需要用户确认
- `task.upload_completed` - 上传完成
- `system.status_updated` - 系统状态更新

### 组件设计需求

#### 1. VideoPlayer 组件
**功能要求**：
- 🎬 支持片段高亮显示
- ⏯️ 支持片段快速跳转
- 📏 支持时间轴标记
- 🔄 支持倍速播放
- 📱 响应式设计

#### 2. TaskStatus 组件
**显示内容**：
- 任务基本信息
- 当前处理状态
- 进度条显示
- 操作按钮（重试、取消等）

#### 3. NotificationCenter 组件
**功能要求**：
- 实时通知显示
- 声音提示控制
- 通知历史记录
- 点击跳转到相关页面

## 💰 成本控制方案

### AI API 成本分析
- **DeepSeek API**：推荐使用，中文友好且便宜
- **OpenAI API**：备选方案，效果更好但成本较高
- **免费模板**：模板化标题生成，零成本

### 多层级方案设计
1. **免费模式**：仅使用模板化生成
2. **基础模式**：DeepSeek API + 模板
3. **高级模式**：OpenAI API + 高级功能

### 成本预估
- **每条视频成本**：$0.005-0.02 (DeepSeek)
- **每日10条视频**：$0.05-0.2
- **每月成本**：$1.5-6

## 🚀 开发计划

### 阶段1：基础框架搭建（2-3周）
1. **调度层开发**（1.5周）
   - FastAPI框架搭建
   - 数据库设计和初始化
   - 基础API接口
   - WebSocket通信

2. **录制库改造**（0.5周）
   - 添加HTTP通知功能
   - 测试与调度层通信

3. **上传服务API化**（1周）
   - 包装现有上传逻辑
   - 添加FastAPI接口

### 阶段2：视频分析服务（3-4周）
1. **精华提取算法**（1.5周）
   - 音频能量分析
   - 多策略片段提取

2. **AI集成**（1周）
   - DeepSeek API集成
   - 标题生成逻辑
   - 免费模板备选

3. **剪映集成**（1周）
   - pyJianYingDraft集成
   - 模板化剪辑

4. **API接口开发**（0.5周）
   - FastAPI接口设计
   - 与调度层集成

### 阶段3：前端界面开发（2-3周）
1. **Vue3项目搭建**（0.5周）
   - 项目初始化
   - 组件库集成
   - 路由配置

2. **核心页面开发**（1.5周）
   - 主监控页面
   - 视频确认页面
   - WebSocket集成

3. **手机端适配**（1周）
   - 响应式设计
   - 移动端优化

### 阶段4：系统集成和优化（1-2周）
1. **整体集成测试**
2. **性能优化**
3. **错误处理完善**
4. **用户体验优化**

## 📁 项目目录结构

```
VideoAnalysis/  (项目根目录)
├── DouyinLiveRecorder-main/     # 录制库 (轻微改动)
├── auto_upload/                 # 上传库 (API包装)
├── scheduler/                   # 调度层 (新开发)
├── video_analysis/             # 视频分析服务 (核心开发)
├── frontend/                   # 前端界面 (Vue3)
├── shared/                     # 共享组件
├── docs/                       # 文档
├── tests/                      # 测试文件
└── README.md                   # 项目说明
```

## ⚙️ 配置管理

### 统一配置文件：`config/system.ini`
**配置分类**：
- 调度层配置
- 视频分析服务配置
- 上传服务配置
- Web界面配置
- 通知设置
- AI服务配置

## 🎯 降低难度的关键措施

### 1. 用户参与度提高
- 👀 **用户观看完整视频**：更好的质量控制
- ✏️ **用户主动调整**：符合个人风格
- 🎯 **降低AI依赖**：减少技术风险

### 2. AI要求降低
- 从"必须准确"改为"提供参考"
- 模板化生成作为主要方案
- AI增强作为可选功能

### 3. 渐进式功能开发
- 基础工作流优先
- AI功能可选
- 高级功能后期迭代

---

**文档版本**：v1.0  
**最后更新**：2024-01-20  
**技术栈**：FastAPI + Vue3 + SQLite + DeepSeek API
