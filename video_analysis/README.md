# 视频分析服务 (VideoAnalysis) - 开发任务文档

## 📋 模块概述

### 职责范围
视频分析服务是系统的核心处理组件，负责：
- 🎯 **精华提取**：从10分钟视频提取2-3分钟精华片段
- 🎯 **内容分析**：AI分析视频内容
- 🎯 **标题生成**：生成3-5个候选标题
- 🎯 **剪映集成**：使用pyJianYingDraft生成草稿
- 🎯 **视频生成**：根据用户选择生成最终视频

### 技术栈
- **框架**：FastAPI
- **AI服务**：DeepSeek API (标题生成)
- **音频处理**：Whisper (语音转文字)
- **视频处理**：OpenCV, FFmpeg
- **剪辑集成**：pyJianYingDraft

## 🏗️ 目录结构

```
video_analysis/
├── app.py                      # FastAPI应用主文件
├── config/
│   ├── __init__.py
│   ├── settings.py             # 配置管理
│   └── ai_config.py            # AI服务配置
├── core/
│   ├── __init__.py
│   ├── extractor.py            # 精华提取器
│   ├── analyzer.py             # 内容分析器
│   ├── generator.py            # 标题生成器
│   ├── jiaying_integrator.py   # 剪映集成器
│   └── video_processor.py      # 视频处理器
├── services/
│   ├── __init__.py
│   ├── analysis_service.py     # 分析服务
│   ├── ai_service.py           # AI服务
│   └── file_service.py         # 文件服务
├── api/
│   ├── __init__.py
│   ├── routes/
│   │   ├── __init__.py
│   │   ├── analysis.py         # 分析相关API
│   │   └── preview.py          # 预览相关API
│   └── schemas/
│       ├── __init__.py
│       ├── analysis_schemas.py # 分析请求响应模型
│       └── result_schemas.py   # 结果模型
├── models/
│   ├── __init__.py
│   ├── video_segment.py        # 视频片段模型
│   ├── analysis_result.py      # 分析结果模型
│   └── title_candidate.py      # 标题候选模型
├── utils/
│   ├── __init__.py
│   ├── video_utils.py          # 视频处理工具
│   ├── audio_utils.py          # 音频处理工具
│   ├── ai_utils.py             # AI工具
│   └── file_utils.py           # 文件工具
├── algorithms/
│   ├── __init__.py
│   ├── energy_detector.py      # 音频能量检测
│   ├── scene_detector.py       # 场景检测
│   ├── keyword_extractor.py    # 关键词提取
│   └── template_generator.py   # 模板生成器
├── tests/
│   ├── __init__.py
│   ├── test_extractor.py
│   ├── test_analyzer.py
│   ├── test_generator.py
│   └── test_api.py
├── temp/                       # 临时文件目录
├── templates/                  # 剪辑模板
├── requirements.txt            # 依赖包
└── README.md                   # 本文档
```

## 📝 开发任务列表

### 1. 项目搭建（预计1天）

#### 1.1 基础环境搭建
- [ ] ⏳ 创建虚拟环境
- [ ] ⏳ 安装FastAPI及相关依赖
- [ ] ⏳ 安装视频处理依赖（OpenCV, FFmpeg）
- [ ] ⏳ 安装AI相关依赖（Whisper, transformers）
- [ ] ⏳ 安装pyJianYingDraft

#### 1.2 项目结构创建
- [ ] ⏳ 创建目录结构
- [ ] ⏳ 初始化各模块文件
- [ ] ⏳ 配置导入路径
- [ ] ⏳ 设置日志系统

### 2. 精华提取算法（预计4天）

#### 2.1 音频能量分析
- [ ] ⏳ 实现音频提取功能
- [ ] ⏳ 实现音频能量计算
- [ ] ⏳ 实现高能量片段检测
- [ ] ⏳ 优化检测算法参数

#### 2.2 视频场景检测
- [ ] ⏳ 实现帧差检测算法
- [ ] ⏳ 实现场景变化检测
- [ ] ⏳ 实现镜头切换检测
- [ ] ⏳ 优化检测精度

#### 2.3 多策略片段提取
- [ ] ⏳ 实现黄金时段提取（2-5分钟）
- [ ] ⏳ 实现中间片段提取
- [ ] ⏳ 实现智能组合算法
- [ ] ⏳ 实现片段评分机制

#### 2.4 候选片段优化
- [ ] ⏳ 实现片段去重算法
- [ ] ⏳ 实现片段质量评估
- [ ] ⏳ 实现最优候选选择
- [ ] ⏳ 添加用户偏好学习

### 3. 内容分析模块（预计3天）

#### 3.1 音频转文字
- [ ] ⏳ 集成Whisper模型
- [ ] ⏳ 实现音频预处理
- [ ] ⏳ 实现批量转录功能
- [ ] ⏳ 优化转录准确性

#### 3.2 关键词提取
- [ ] ⏳ 实现TF-IDF关键词提取
- [ ] ⏳ 实现基于词频的提取
- [ ] ⏳ 实现停用词过滤
- [ ] ⏳ 实现关键词权重计算

#### 3.3 内容类型分类
- [ ] ⏳ 定义内容类型（游戏、唱歌、聊天等）
- [ ] ⏳ 实现基于关键词的分类
- [ ] ⏳ 实现基于音频特征的分类
- [ ] ⏳ 优化分类准确性

#### 3.4 场景分析
- [ ] ⏳ 实现视觉场景分析
- [ ] ⏳ 实现人物检测
- [ ] ⏳ 实现动作识别
- [ ] ⏳ 实现场景描述生成

### 4. 标题生成器（预计3天）

#### 4.1 模板化标题生成
- [ ] ⏳ 设计标题模板库
- [ ] ⏳ 实现关键词替换算法
- [ ] ⏳ 实现模板选择逻辑
- [ ] ⏳ 优化模板质量

#### 4.2 AI标题生成
- [ ] ⏳ 集成DeepSeek API
- [ ] ⏳ 设计Prompt模板
- [ ] ⏳ 实现API调用封装
- [ ] ⏳ 添加API错误处理

#### 4.3 标题质量评估
- [ ] ⏳ 实现标题长度检查
- [ ] ⏳ 实现关键词覆盖检查
- [ ] ⏳ 实现吸引力评分
- [ ] ⏳ 实现重复度检查

#### 4.4 多候选生成
- [ ] ⏳ 实现多样化生成策略
- [ ] ⏳ 实现候选排序算法
- [ ] ⏳ 实现候选去重
- [ ] ⏳ 优化候选质量

### 5. 剪映集成（预计3天）

#### 5.1 pyJianYingDraft集成
- [ ] ⏳ 研究pyJianYingDraft API
- [ ] ⏳ 实现基础草稿创建
- [ ] ⏳ 实现视频片段添加
- [ ] ⏳ 测试草稿生成功能

#### 5.2 剪辑模板设计
- [ ] ⏳ 设计基础剪辑模板
- [ ] ⏳ 实现1.2倍速设置
- [ ] ⏳ 实现水印添加
- [ ] ⏳ 实现字幕添加

#### 5.3 自动化剪辑流程
- [ ] ⏳ 实现模板应用流程
- [ ] ⏳ 实现参数自定义
- [ ] ⏳ 实现批量处理
- [ ] ⏳ 优化处理速度

#### 5.4 视频导出功能
- [ ] ⏳ 实现草稿保存
- [ ] ⏳ 实现视频导出
- [ ] ⏳ 实现导出状态监控
- [ ] ⏳ 添加导出错误处理

### 6. API接口开发（预计2天）

#### 6.1 分析相关API
- [ ] ⏳ POST /api/v1/analyze/start
- [ ] ⏳ GET /api/v1/analyze/status/{task_id}
- [ ] ⏳ GET /api/v1/analyze/result/{task_id}
- [ ] ⏳ POST /api/v1/analyze/generate

#### 6.2 预览相关API
- [ ] ⏳ GET /api/v1/preview/{task_id}/segments
- [ ] ⏳ GET /api/v1/preview/{task_id}/segment/{segment_id}
- [ ] ⏳ GET /api/v1/preview/{task_id}/titles

#### 6.3 配置相关API
- [ ] ⏳ GET /api/v1/config/templates
- [ ] ⏳ POST /api/v1/config/templates
- [ ] ⏳ GET /api/v1/config/ai-settings

### 7. 错误处理和测试（预计2天）

#### 7.1 错误处理机制
- [ ] ⏳ 实现全局异常处理
- [ ] ⏳ 添加业务异常定义
- [ ] ⏳ 实现错误日志记录
- [ ] ⏳ 添加错误恢复机制

#### 7.2 单元测试
- [ ] ⏳ 编写精华提取测试
- [ ] ⏳ 编写内容分析测试
- [ ] ⏳ 编写标题生成测试
- [ ] ⏳ 编写API接口测试

## 🔧 技术实现要点

### 1. 精华提取算法
```python
class HighlightExtractor:
    def extract_highlights(self, video_path: str) -> List[VideoSegment]:
        """提取精华片段"""
        # 策略1: 音频能量检测
        audio_segments = self.extract_by_audio_energy(video_path)
        
        # 策略2: 黄金时段
        golden_segments = self.extract_golden_time(video_path)
        
        # 策略3: 场景变化
        scene_segments = self.extract_by_scene_change(video_path)
        
        # 综合评分和选择
        return self.select_best_segments([
            audio_segments, golden_segments, scene_segments
        ])
```

### 2. AI标题生成
```python
class TitleGenerator:
    def generate_ai_titles(self, content_analysis: dict) -> List[str]:
        """使用AI生成标题"""
        prompt = f"""
        基于以下直播内容分析，生成3个吸引人的标题：
        
        内容类型：{content_analysis['content_type']}
        关键词：{content_analysis['keywords']}
        转录文本：{content_analysis['transcript'][:500]}
        
        要求：
        1. 标题长度15-30字
        2. 包含关键词
        3. 吸引眼球
        4. 符合快手平台风格
        """
        
        return self.call_deepseek_api(prompt)
```

### 3. 剪映集成
```python
class JianyingIntegrator:
    def create_draft(self, segment: VideoSegment, title: str) -> str:
        """创建剪映草稿"""
        import pyJianYingDraft as draft
        
        script = draft.ScriptFile(1080, 1920)  # 竖屏
        
        # 添加视频片段
        video_seg = draft.VideoSegment(
            segment.video_path,
            start_time=segment.start_time,
            duration=segment.duration,
            speed=1.2  # 1.2倍速
        )
        script.add_segment(video_seg)
        
        # 添加字幕
        if segment.transcript:
            subtitle = draft.Subtitle(segment.transcript)
            script.add_subtitle(subtitle)
        
        # 添加水印
        watermark = draft.Watermark("./templates/watermark.png")
        script.add_watermark(watermark)
        
        return script.save(f"./temp/{title}.json")
```

## 📊 性能要求

### 1. 处理时间
- 10分钟视频分析时间 < 2分钟
- 精华提取时间 < 30秒
- 标题生成时间 < 10秒
- 剪映草稿生成 < 15秒

### 2. 准确性要求
- 精华片段质量 > 70%
- 标题相关性 > 80%
- 内容分类准确性 > 85%

### 3. 资源使用
- 内存使用 < 4GB
- GPU使用率 < 80%
- 临时文件及时清理

## 🧪 测试策略

### 1. 算法测试
- 精华提取准确性测试
- 标题生成质量测试
- 内容分析准确性测试

### 2. 性能测试
- 处理速度测试
- 内存使用测试
- 并发处理测试

### 3. 集成测试
- 与调度层集成测试
- 剪映集成测试
- API接口测试

## 📝 开发注意事项

### 1. AI服务使用
- 合理控制API调用频率
- 实现API调用失败重试
- 添加本地缓存机制
- 监控API使用成本

### 2. 视频处理优化
- 使用GPU加速（如果可用）
- 实现多线程处理
- 优化内存使用
- 及时清理临时文件

### 3. 算法调优
- 收集用户反馈数据
- 持续优化算法参数
- A/B测试不同策略
- 建立评估指标体系

---

**模块负责人**：开发者  
**预计开发周期**：3-4周  
**依赖模块**：调度层  
**被依赖模块**：前端界面
